"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-three";
exports.ids = ["vendor-chunks/@react-three"];
exports.modules = {

/***/ "(ssr)/./node_modules/@react-three/fiber/dist/events-cf57b220.esm.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@react-three/fiber/dist/events-cf57b220.esm.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (/* binding */ useStore),\n/* harmony export */   B: () => (/* binding */ Block),\n/* harmony export */   C: () => (/* binding */ useThree),\n/* harmony export */   D: () => (/* binding */ useFrame),\n/* harmony export */   E: () => (/* binding */ ErrorBoundary),\n/* harmony export */   F: () => (/* binding */ useGraph),\n/* harmony export */   G: () => (/* binding */ useLoader),\n/* harmony export */   _: () => (/* binding */ _roots),\n/* harmony export */   a: () => (/* binding */ useMutableCallback),\n/* harmony export */   b: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   c: () => (/* binding */ createRoot),\n/* harmony export */   d: () => (/* binding */ unmountComponentAtNode),\n/* harmony export */   e: () => (/* binding */ extend),\n/* harmony export */   f: () => (/* binding */ createPointerEvents),\n/* harmony export */   g: () => (/* binding */ createEvents),\n/* harmony export */   h: () => (/* binding */ flushGlobalEffects),\n/* harmony export */   i: () => (/* binding */ isRef),\n/* harmony export */   j: () => (/* binding */ addEffect),\n/* harmony export */   k: () => (/* binding */ addAfterEffect),\n/* harmony export */   l: () => (/* binding */ addTail),\n/* harmony export */   m: () => (/* binding */ invalidate),\n/* harmony export */   n: () => (/* binding */ advance),\n/* harmony export */   o: () => (/* binding */ createPortal),\n/* harmony export */   p: () => (/* binding */ flushSync),\n/* harmony export */   q: () => (/* binding */ context),\n/* harmony export */   r: () => (/* binding */ reconciler),\n/* harmony export */   s: () => (/* binding */ applyProps),\n/* harmony export */   t: () => (/* binding */ threeTypes),\n/* harmony export */   u: () => (/* binding */ useBridge),\n/* harmony export */   v: () => (/* binding */ getRootState),\n/* harmony export */   w: () => (/* binding */ dispose),\n/* harmony export */   x: () => (/* binding */ act),\n/* harmony export */   y: () => (/* binding */ buildGraph),\n/* harmony export */   z: () => (/* binding */ useInstanceHandle)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-reconciler/constants */ \"(ssr)/./node_modules/react-reconciler/constants.js\");\n/* harmony import */ var zustand_traditional__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! zustand/traditional */ \"(ssr)/./node_modules/zustand/esm/traditional.mjs\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-reconciler */ \"(ssr)/./node_modules/react-reconciler/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js\");\n/* harmony import */ var suspend_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! suspend-react */ \"(ssr)/./node_modules/suspend-react/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var its_fine__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! its-fine */ \"(ssr)/./node_modules/its-fine/dist/index.js\");\n\n\n\n\n\n\n\n\n\nvar threeTypes = /*#__PURE__*/ Object.freeze({\n    __proto__: null\n});\n/**\n * Returns the instance's initial (outmost) root.\n */ function findInitialRoot(instance) {\n    let root = instance.root;\n    while(root.getState().previousRoot)root = root.getState().previousRoot;\n    return root;\n}\n/**\n * Safely flush async effects when testing, simulating a legacy root.\n * @deprecated Import from React instead. import { act } from 'react'\n */ // Reference with computed key to break Webpack static analysis\n// https://github.com/webpack/webpack/issues/14814\nconst act = react__WEBPACK_IMPORTED_MODULE_0__['act' + ''];\nconst isOrthographicCamera = (def)=>def && def.isOrthographicCamera;\nconst isRef = (obj)=>obj && obj.hasOwnProperty('current');\nconst isColorRepresentation = (value)=>value != null && (typeof value === 'string' || typeof value === 'number' || value.isColor);\n/**\n * An SSR-friendly useLayoutEffect.\n *\n * React currently throws a warning when using useLayoutEffect on the server.\n * To get around it, we can conditionally useEffect on the server (no-op) and\n * useLayoutEffect elsewhere.\n *\n * @see https://github.com/facebook/react/issues/14927\n */ const useIsomorphicLayoutEffect = /* @__PURE__ */ ((_window$document, _window$navigator)=> false && (0))() ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction useMutableCallback(fn) {\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fn);\n    useIsomorphicLayoutEffect({\n        \"useMutableCallback.useIsomorphicLayoutEffect\": ()=>void (ref.current = fn)\n    }[\"useMutableCallback.useIsomorphicLayoutEffect\"], [\n        fn\n    ]);\n    return ref;\n}\n/**\n * Bridges renderer Context and StrictMode from a primary renderer.\n */ function useBridge() {\n    const fiber = (0,its_fine__WEBPACK_IMPORTED_MODULE_5__.useFiber)();\n    const ContextBridge = (0,its_fine__WEBPACK_IMPORTED_MODULE_5__.useContextBridge)();\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"useBridge.useMemo\": ()=>({\n                \"useBridge.useMemo\": ({ children })=>{\n                    const strict = !!(0,its_fine__WEBPACK_IMPORTED_MODULE_5__.traverseFiber)(fiber, true, {\n                        \"useBridge.useMemo\": (node)=>node.type === react__WEBPACK_IMPORTED_MODULE_0__.StrictMode\n                    }[\"useBridge.useMemo\"]);\n                    const Root = strict ? react__WEBPACK_IMPORTED_MODULE_0__.StrictMode : react__WEBPACK_IMPORTED_MODULE_0__.Fragment;\n                    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Root, {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ContextBridge, {\n                            children: children\n                        })\n                    });\n                }\n            })[\"useBridge.useMemo\"]\n    }[\"useBridge.useMemo\"], [\n        fiber,\n        ContextBridge\n    ]);\n}\nfunction Block({ set }) {\n    useIsomorphicLayoutEffect({\n        \"Block.useIsomorphicLayoutEffect\": ()=>{\n            set(new Promise({\n                \"Block.useIsomorphicLayoutEffect\": ()=>null\n            }[\"Block.useIsomorphicLayoutEffect\"]));\n            return ({\n                \"Block.useIsomorphicLayoutEffect\": ()=>set(false)\n            })[\"Block.useIsomorphicLayoutEffect\"];\n        }\n    }[\"Block.useIsomorphicLayoutEffect\"], [\n        set\n    ]);\n    return null;\n}\n// NOTE: static members get down-level transpiled to mutations which break tree-shaking\nconst ErrorBoundary = /* @__PURE__ */ ((_ErrorBoundary)=>(_ErrorBoundary = class ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n        constructor(...args){\n            super(...args);\n            this.state = {\n                error: false\n            };\n        }\n        componentDidCatch(err) {\n            this.props.set(err);\n        }\n        render() {\n            return this.state.error ? null : this.props.children;\n        }\n    }, _ErrorBoundary.getDerivedStateFromError = ()=>({\n            error: true\n        }), _ErrorBoundary))();\nfunction calculateDpr(dpr) {\n    var _window$devicePixelRa;\n    // Err on the side of progress by assuming 2x dpr if we can't detect it\n    // This will happen in workers where window is defined but dpr isn't.\n    const target =  false ? 0 : 1;\n    return Array.isArray(dpr) ? Math.min(Math.max(dpr[0], target), dpr[1]) : dpr;\n}\n/**\n * Returns instance root state\n */ function getRootState(obj) {\n    var _r3f;\n    return (_r3f = obj.__r3f) == null ? void 0 : _r3f.root.getState();\n}\n// A collection of compare functions\nconst is = {\n    obj: (a)=>a === Object(a) && !is.arr(a) && typeof a !== 'function',\n    fun: (a)=>typeof a === 'function',\n    str: (a)=>typeof a === 'string',\n    num: (a)=>typeof a === 'number',\n    boo: (a)=>typeof a === 'boolean',\n    und: (a)=>a === void 0,\n    nul: (a)=>a === null,\n    arr: (a)=>Array.isArray(a),\n    equ (a, b, { arrays = 'shallow', objects = 'reference', strict = true } = {}) {\n        // Wrong type or one of the two undefined, doesn't match\n        if (typeof a !== typeof b || !!a !== !!b) return false;\n        // Atomic, just compare a against b\n        if (is.str(a) || is.num(a) || is.boo(a)) return a === b;\n        const isObj = is.obj(a);\n        if (isObj && objects === 'reference') return a === b;\n        const isArr = is.arr(a);\n        if (isArr && arrays === 'reference') return a === b;\n        // Array or Object, shallow compare first to see if it's a match\n        if ((isArr || isObj) && a === b) return true;\n        // Last resort, go through keys\n        let i;\n        // Check if a has all the keys of b\n        for(i in a)if (!(i in b)) return false;\n        // Check if values between keys match\n        if (isObj && arrays === 'shallow' && objects === 'shallow') {\n            for(i in strict ? b : a)if (!is.equ(a[i], b[i], {\n                strict,\n                objects: 'reference'\n            })) return false;\n        } else {\n            for(i in strict ? b : a)if (a[i] !== b[i]) return false;\n        }\n        // If i is undefined\n        if (is.und(i)) {\n            // If both arrays are empty we consider them equal\n            if (isArr && a.length === 0 && b.length === 0) return true;\n            // If both objects are empty we consider them equal\n            if (isObj && Object.keys(a).length === 0 && Object.keys(b).length === 0) return true;\n            // Otherwise match them by value\n            if (a !== b) return false;\n        }\n        return true;\n    }\n};\n// Collects nodes and materials from a THREE.Object3D\nfunction buildGraph(object) {\n    const data = {\n        nodes: {},\n        materials: {},\n        meshes: {}\n    };\n    if (object) {\n        object.traverse((obj)=>{\n            if (obj.name) data.nodes[obj.name] = obj;\n            if (obj.material && !data.materials[obj.material.name]) data.materials[obj.material.name] = obj.material;\n            if (obj.isMesh && !data.meshes[obj.name]) data.meshes[obj.name] = obj;\n        });\n    }\n    return data;\n}\n// Disposes an object and all its properties\nfunction dispose(obj) {\n    if (obj.type !== 'Scene') obj.dispose == null ? void 0 : obj.dispose();\n    for(const p in obj){\n        const prop = obj[p];\n        if ((prop == null ? void 0 : prop.type) !== 'Scene') prop == null ? void 0 : prop.dispose == null ? void 0 : prop.dispose();\n    }\n}\nconst REACT_INTERNAL_PROPS = [\n    'children',\n    'key',\n    'ref'\n];\n// Gets only instance props from reconciler fibers\nfunction getInstanceProps(queue) {\n    const props = {};\n    for(const key in queue){\n        if (!REACT_INTERNAL_PROPS.includes(key)) props[key] = queue[key];\n    }\n    return props;\n}\n// Each object in the scene carries a small LocalState descriptor\nfunction prepare(target, root, type, props) {\n    const object = target;\n    // Create instance descriptor\n    let instance = object == null ? void 0 : object.__r3f;\n    if (!instance) {\n        instance = {\n            root,\n            type,\n            parent: null,\n            children: [],\n            props: getInstanceProps(props),\n            object,\n            eventCount: 0,\n            handlers: {},\n            isHidden: false\n        };\n        if (object) object.__r3f = instance;\n    }\n    return instance;\n}\nfunction resolve(root, key) {\n    let target = root[key];\n    if (!key.includes('-')) return {\n        root,\n        key,\n        target\n    };\n    // Resolve pierced target\n    target = root;\n    for (const part of key.split('-')){\n        var _target;\n        key = part;\n        root = target;\n        target = (_target = target) == null ? void 0 : _target[key];\n    }\n    // TODO: change key to 'foo-bar' if target is undefined?\n    return {\n        root,\n        key,\n        target\n    };\n}\n// Checks if a dash-cased string ends with an integer\nconst INDEX_REGEX = /-\\d+$/;\nfunction attach(parent, child) {\n    if (is.str(child.props.attach)) {\n        // If attaching into an array (foo-0), create one\n        if (INDEX_REGEX.test(child.props.attach)) {\n            const index = child.props.attach.replace(INDEX_REGEX, '');\n            const { root, key } = resolve(parent.object, index);\n            if (!Array.isArray(root[key])) root[key] = [];\n        }\n        const { root, key } = resolve(parent.object, child.props.attach);\n        child.previousAttach = root[key];\n        root[key] = child.object;\n    } else if (is.fun(child.props.attach)) {\n        child.previousAttach = child.props.attach(parent.object, child.object);\n    }\n}\nfunction detach(parent, child) {\n    if (is.str(child.props.attach)) {\n        const { root, key } = resolve(parent.object, child.props.attach);\n        const previous = child.previousAttach;\n        // When the previous value was undefined, it means the value was never set to begin with\n        if (previous === undefined) delete root[key];\n        else root[key] = previous;\n    } else {\n        child.previousAttach == null ? void 0 : child.previousAttach(parent.object, child.object);\n    }\n    delete child.previousAttach;\n}\nconst RESERVED_PROPS = [\n    ...REACT_INTERNAL_PROPS,\n    // Instance props\n    'args',\n    'dispose',\n    'attach',\n    'object',\n    'onUpdate',\n    // Behavior flags\n    'dispose'\n];\nconst MEMOIZED_PROTOTYPES = new Map();\nfunction getMemoizedPrototype(root) {\n    let ctor = MEMOIZED_PROTOTYPES.get(root.constructor);\n    try {\n        if (!ctor) {\n            ctor = new root.constructor();\n            MEMOIZED_PROTOTYPES.set(root.constructor, ctor);\n        }\n    } catch (e) {\n    // ...\n    }\n    return ctor;\n}\n// This function prepares a set of changes to be applied to the instance\nfunction diffProps(instance, newProps) {\n    const changedProps = {};\n    // Sort through props\n    for(const prop in newProps){\n        // Skip reserved keys\n        if (RESERVED_PROPS.includes(prop)) continue;\n        // Skip if props match\n        if (is.equ(newProps[prop], instance.props[prop])) continue;\n        // Props changed, add them\n        changedProps[prop] = newProps[prop];\n        // Reset pierced props\n        for(const other in newProps){\n            if (other.startsWith(`${prop}-`)) changedProps[other] = newProps[other];\n        }\n    }\n    // Reset removed props for HMR\n    for(const prop in instance.props){\n        if (RESERVED_PROPS.includes(prop) || newProps.hasOwnProperty(prop)) continue;\n        const { root, key } = resolve(instance.object, prop);\n        // https://github.com/mrdoob/three.js/issues/21209\n        // HMR/fast-refresh relies on the ability to cancel out props, but threejs\n        // has no means to do this. Hence we curate a small collection of value-classes\n        // with their respective constructor/set arguments\n        // For removed props, try to set default values, if possible\n        if (root.constructor && root.constructor.length === 0) {\n            // create a blank slate of the instance and copy the particular parameter.\n            const ctor = getMemoizedPrototype(root);\n            if (!is.und(ctor)) changedProps[key] = ctor[key];\n        } else {\n            // instance does not have constructor, just set it to 0\n            changedProps[key] = 0;\n        }\n    }\n    return changedProps;\n}\n// https://github.com/mrdoob/three.js/pull/27042\n// https://github.com/mrdoob/three.js/pull/22748\nconst colorMaps = [\n    'map',\n    'emissiveMap',\n    'sheenColorMap',\n    'specularColorMap',\n    'envMap'\n];\nconst EVENT_REGEX = /^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;\n// This function applies a set of changes to the instance\nfunction applyProps(object, props) {\n    var _instance$object;\n    const instance = object.__r3f;\n    const rootState = instance && findInitialRoot(instance).getState();\n    const prevHandlers = instance == null ? void 0 : instance.eventCount;\n    for(const prop in props){\n        let value = props[prop];\n        // Don't mutate reserved keys\n        if (RESERVED_PROPS.includes(prop)) continue;\n        // Deal with pointer events, including removing them if undefined\n        if (instance && EVENT_REGEX.test(prop)) {\n            if (typeof value === 'function') instance.handlers[prop] = value;\n            else delete instance.handlers[prop];\n            instance.eventCount = Object.keys(instance.handlers).length;\n            continue;\n        }\n        // Ignore setting undefined props\n        // https://github.com/pmndrs/react-three-fiber/issues/274\n        if (value === undefined) continue;\n        let { root, key, target } = resolve(object, prop);\n        // Layers must be written to the mask property\n        if (target instanceof three__WEBPACK_IMPORTED_MODULE_6__.Layers && value instanceof three__WEBPACK_IMPORTED_MODULE_6__.Layers) {\n            target.mask = value.mask;\n        } else if (target instanceof three__WEBPACK_IMPORTED_MODULE_6__.Color && isColorRepresentation(value)) {\n            target.set(value);\n        } else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && typeof target.copy === 'function' && value != null && value.constructor && target.constructor === value.constructor) {\n            target.copy(value);\n        } else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && Array.isArray(value)) {\n            if (typeof target.fromArray === 'function') target.fromArray(value);\n            else target.set(...value);\n        } else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && typeof value === 'number') {\n            // Allow setting array scalars\n            if (typeof target.setScalar === 'function') target.setScalar(value);\n            else target.set(value);\n        } else {\n            var _root$key;\n            root[key] = value;\n            // Auto-convert sRGB texture parameters for built-in materials\n            // https://github.com/pmndrs/react-three-fiber/issues/344\n            // https://github.com/mrdoob/three.js/pull/25857\n            if (rootState && !rootState.linear && colorMaps.includes(key) && (_root$key = root[key]) != null && _root$key.isTexture && // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n            root[key].format === three__WEBPACK_IMPORTED_MODULE_6__.RGBAFormat && root[key].type === three__WEBPACK_IMPORTED_MODULE_6__.UnsignedByteType) {\n                // NOTE: this cannot be set from the renderer (e.g. sRGB source textures rendered to P3)\n                root[key].colorSpace = three__WEBPACK_IMPORTED_MODULE_6__.SRGBColorSpace;\n            }\n        }\n    }\n    // Register event handlers\n    if (instance != null && instance.parent && rootState != null && rootState.internal && (_instance$object = instance.object) != null && _instance$object.isObject3D && prevHandlers !== instance.eventCount) {\n        const object = instance.object;\n        // Pre-emptively remove the instance from the interaction manager\n        const index = rootState.internal.interaction.indexOf(object);\n        if (index > -1) rootState.internal.interaction.splice(index, 1);\n        // Add the instance to the interaction manager only when it has handlers\n        if (instance.eventCount && object.raycast !== null) {\n            rootState.internal.interaction.push(object);\n        }\n    }\n    // Auto-attach geometries and materials\n    if (instance && instance.props.attach === undefined) {\n        if (instance.object.isBufferGeometry) instance.props.attach = 'geometry';\n        else if (instance.object.isMaterial) instance.props.attach = 'material';\n    }\n    // Instance was updated, request a frame\n    if (instance) invalidateInstance(instance);\n    return object;\n}\nfunction invalidateInstance(instance) {\n    var _instance$root;\n    if (!instance.parent) return;\n    instance.props.onUpdate == null ? void 0 : instance.props.onUpdate(instance.object);\n    const state = (_instance$root = instance.root) == null ? void 0 : _instance$root.getState == null ? void 0 : _instance$root.getState();\n    if (state && state.internal.frames === 0) state.invalidate();\n}\nfunction updateCamera(camera, size) {\n    // Do not mess with the camera if it belongs to the user\n    // https://github.com/pmndrs/react-three-fiber/issues/92\n    if (camera.manual) return;\n    if (isOrthographicCamera(camera)) {\n        camera.left = size.width / -2;\n        camera.right = size.width / 2;\n        camera.top = size.height / 2;\n        camera.bottom = size.height / -2;\n    } else {\n        camera.aspect = size.width / size.height;\n    }\n    camera.updateProjectionMatrix();\n}\nconst isObject3D = (object)=>object == null ? void 0 : object.isObject3D;\nfunction makeId(event) {\n    return (event.eventObject || event.object).uuid + '/' + event.index + event.instanceId;\n}\n/**\n * Release pointer captures.\n * This is called by releasePointerCapture in the API, and when an object is removed.\n */ function releaseInternalPointerCapture(capturedMap, obj, captures, pointerId) {\n    const captureData = captures.get(obj);\n    if (captureData) {\n        captures.delete(obj);\n        // If this was the last capturing object for this pointer\n        if (captures.size === 0) {\n            capturedMap.delete(pointerId);\n            captureData.target.releasePointerCapture(pointerId);\n        }\n    }\n}\nfunction removeInteractivity(store, object) {\n    const { internal } = store.getState();\n    // Removes every trace of an object from the data store\n    internal.interaction = internal.interaction.filter((o)=>o !== object);\n    internal.initialHits = internal.initialHits.filter((o)=>o !== object);\n    internal.hovered.forEach((value, key)=>{\n        if (value.eventObject === object || value.object === object) {\n            // Clear out intersects, they are outdated by now\n            internal.hovered.delete(key);\n        }\n    });\n    internal.capturedMap.forEach((captures, pointerId)=>{\n        releaseInternalPointerCapture(internal.capturedMap, object, captures, pointerId);\n    });\n}\nfunction createEvents(store) {\n    /** Calculates delta */ function calculateDistance(event) {\n        const { internal } = store.getState();\n        const dx = event.offsetX - internal.initialClick[0];\n        const dy = event.offsetY - internal.initialClick[1];\n        return Math.round(Math.sqrt(dx * dx + dy * dy));\n    }\n    /** Returns true if an instance has a valid pointer-event registered, this excludes scroll, clicks etc */ function filterPointerEvents(objects) {\n        return objects.filter((obj)=>[\n                'Move',\n                'Over',\n                'Enter',\n                'Out',\n                'Leave'\n            ].some((name)=>{\n                var _r3f;\n                return (_r3f = obj.__r3f) == null ? void 0 : _r3f.handlers['onPointer' + name];\n            }));\n    }\n    function intersect(event, filter) {\n        const state = store.getState();\n        const duplicates = new Set();\n        const intersections = [];\n        // Allow callers to eliminate event objects\n        const eventsObjects = filter ? filter(state.internal.interaction) : state.internal.interaction;\n        // Reset all raycaster cameras to undefined\n        for(let i = 0; i < eventsObjects.length; i++){\n            const state = getRootState(eventsObjects[i]);\n            if (state) {\n                state.raycaster.camera = undefined;\n            }\n        }\n        if (!state.previousRoot) {\n            // Make sure root-level pointer and ray are set up\n            state.events.compute == null ? void 0 : state.events.compute(event, state);\n        }\n        function handleRaycast(obj) {\n            const state = getRootState(obj);\n            // Skip event handling when noEvents is set, or when the raycasters camera is null\n            if (!state || !state.events.enabled || state.raycaster.camera === null) return [];\n            // When the camera is undefined we have to call the event layers update function\n            if (state.raycaster.camera === undefined) {\n                var _state$previousRoot;\n                state.events.compute == null ? void 0 : state.events.compute(event, state, (_state$previousRoot = state.previousRoot) == null ? void 0 : _state$previousRoot.getState());\n                // If the camera is still undefined we have to skip this layer entirely\n                if (state.raycaster.camera === undefined) state.raycaster.camera = null;\n            }\n            // Intersect object by object\n            return state.raycaster.camera ? state.raycaster.intersectObject(obj, true) : [];\n        }\n        // Collect events\n        let hits = eventsObjects// Intersect objects\n        .flatMap(handleRaycast)// Sort by event priority and distance\n        .sort((a, b)=>{\n            const aState = getRootState(a.object);\n            const bState = getRootState(b.object);\n            if (!aState || !bState) return a.distance - b.distance;\n            return bState.events.priority - aState.events.priority || a.distance - b.distance;\n        })// Filter out duplicates\n        .filter((item)=>{\n            const id = makeId(item);\n            if (duplicates.has(id)) return false;\n            duplicates.add(id);\n            return true;\n        });\n        // https://github.com/mrdoob/three.js/issues/16031\n        // Allow custom userland intersect sort order, this likely only makes sense on the root filter\n        if (state.events.filter) hits = state.events.filter(hits, state);\n        // Bubble up the events, find the event source (eventObject)\n        for (const hit of hits){\n            let eventObject = hit.object;\n            // Bubble event up\n            while(eventObject){\n                var _r3f2;\n                if ((_r3f2 = eventObject.__r3f) != null && _r3f2.eventCount) intersections.push({\n                    ...hit,\n                    eventObject\n                });\n                eventObject = eventObject.parent;\n            }\n        }\n        // If the interaction is captured, make all capturing targets part of the intersect.\n        if ('pointerId' in event && state.internal.capturedMap.has(event.pointerId)) {\n            for (let captureData of state.internal.capturedMap.get(event.pointerId).values()){\n                if (!duplicates.has(makeId(captureData.intersection))) intersections.push(captureData.intersection);\n            }\n        }\n        return intersections;\n    }\n    /**  Handles intersections by forwarding them to handlers */ function handleIntersects(intersections, event, delta, callback) {\n        // If anything has been found, forward it to the event listeners\n        if (intersections.length) {\n            const localState = {\n                stopped: false\n            };\n            for (const hit of intersections){\n                let state = getRootState(hit.object);\n                // If the object is not managed by R3F, it might be parented to an element which is.\n                // Traverse upwards until we find a managed parent and use its state instead.\n                if (!state) {\n                    hit.object.traverseAncestors((obj)=>{\n                        const parentState = getRootState(obj);\n                        if (parentState) {\n                            state = parentState;\n                            return false;\n                        }\n                    });\n                }\n                if (state) {\n                    const { raycaster, pointer, camera, internal } = state;\n                    const unprojectedPoint = new three__WEBPACK_IMPORTED_MODULE_6__.Vector3(pointer.x, pointer.y, 0).unproject(camera);\n                    const hasPointerCapture = (id)=>{\n                        var _internal$capturedMap, _internal$capturedMap2;\n                        return (_internal$capturedMap = (_internal$capturedMap2 = internal.capturedMap.get(id)) == null ? void 0 : _internal$capturedMap2.has(hit.eventObject)) != null ? _internal$capturedMap : false;\n                    };\n                    const setPointerCapture = (id)=>{\n                        const captureData = {\n                            intersection: hit,\n                            target: event.target\n                        };\n                        if (internal.capturedMap.has(id)) {\n                            // if the pointerId was previously captured, we add the hit to the\n                            // event capturedMap.\n                            internal.capturedMap.get(id).set(hit.eventObject, captureData);\n                        } else {\n                            // if the pointerId was not previously captured, we create a map\n                            // containing the hitObject, and the hit. hitObject is used for\n                            // faster access.\n                            internal.capturedMap.set(id, new Map([\n                                [\n                                    hit.eventObject,\n                                    captureData\n                                ]\n                            ]));\n                        }\n                        event.target.setPointerCapture(id);\n                    };\n                    const releasePointerCapture = (id)=>{\n                        const captures = internal.capturedMap.get(id);\n                        if (captures) {\n                            releaseInternalPointerCapture(internal.capturedMap, hit.eventObject, captures, id);\n                        }\n                    };\n                    // Add native event props\n                    let extractEventProps = {};\n                    // This iterates over the event's properties including the inherited ones. Native PointerEvents have most of their props as getters which are inherited, but polyfilled PointerEvents have them all as their own properties (i.e. not inherited). We can't use Object.keys() or Object.entries() as they only return \"own\" properties; nor Object.getPrototypeOf(event) as that *doesn't* return \"own\" properties, only inherited ones.\n                    for(let prop in event){\n                        let property = event[prop];\n                        // Only copy over atomics, leave functions alone as these should be\n                        // called as event.nativeEvent.fn()\n                        if (typeof property !== 'function') extractEventProps[prop] = property;\n                    }\n                    let raycastEvent = {\n                        ...hit,\n                        ...extractEventProps,\n                        pointer,\n                        intersections,\n                        stopped: localState.stopped,\n                        delta,\n                        unprojectedPoint,\n                        ray: raycaster.ray,\n                        camera: camera,\n                        // Hijack stopPropagation, which just sets a flag\n                        stopPropagation () {\n                            // https://github.com/pmndrs/react-three-fiber/issues/596\n                            // Events are not allowed to stop propagation if the pointer has been captured\n                            const capturesForPointer = 'pointerId' in event && internal.capturedMap.get(event.pointerId);\n                            // We only authorize stopPropagation...\n                            if (// ...if this pointer hasn't been captured\n                            !capturesForPointer || // ... or if the hit object is capturing the pointer\n                            capturesForPointer.has(hit.eventObject)) {\n                                raycastEvent.stopped = localState.stopped = true;\n                                // Propagation is stopped, remove all other hover records\n                                // An event handler is only allowed to flush other handlers if it is hovered itself\n                                if (internal.hovered.size && Array.from(internal.hovered.values()).find((i)=>i.eventObject === hit.eventObject)) {\n                                    // Objects cannot flush out higher up objects that have already caught the event\n                                    const higher = intersections.slice(0, intersections.indexOf(hit));\n                                    cancelPointer([\n                                        ...higher,\n                                        hit\n                                    ]);\n                                }\n                            }\n                        },\n                        // there should be a distinction between target and currentTarget\n                        target: {\n                            hasPointerCapture,\n                            setPointerCapture,\n                            releasePointerCapture\n                        },\n                        currentTarget: {\n                            hasPointerCapture,\n                            setPointerCapture,\n                            releasePointerCapture\n                        },\n                        nativeEvent: event\n                    };\n                    // Call subscribers\n                    callback(raycastEvent);\n                    // Event bubbling may be interrupted by stopPropagation\n                    if (localState.stopped === true) break;\n                }\n            }\n        }\n        return intersections;\n    }\n    function cancelPointer(intersections) {\n        const { internal } = store.getState();\n        for (const hoveredObj of internal.hovered.values()){\n            // When no objects were hit or the the hovered object wasn't found underneath the cursor\n            // we call onPointerOut and delete the object from the hovered-elements map\n            if (!intersections.length || !intersections.find((hit)=>hit.object === hoveredObj.object && hit.index === hoveredObj.index && hit.instanceId === hoveredObj.instanceId)) {\n                const eventObject = hoveredObj.eventObject;\n                const instance = eventObject.__r3f;\n                internal.hovered.delete(makeId(hoveredObj));\n                if (instance != null && instance.eventCount) {\n                    const handlers = instance.handlers;\n                    // Clear out intersects, they are outdated by now\n                    const data = {\n                        ...hoveredObj,\n                        intersections\n                    };\n                    handlers.onPointerOut == null ? void 0 : handlers.onPointerOut(data);\n                    handlers.onPointerLeave == null ? void 0 : handlers.onPointerLeave(data);\n                }\n            }\n        }\n    }\n    function pointerMissed(event, objects) {\n        for(let i = 0; i < objects.length; i++){\n            const instance = objects[i].__r3f;\n            instance == null ? void 0 : instance.handlers.onPointerMissed == null ? void 0 : instance.handlers.onPointerMissed(event);\n        }\n    }\n    function handlePointer(name) {\n        // Deal with cancelation\n        switch(name){\n            case 'onPointerLeave':\n            case 'onPointerCancel':\n                return ()=>cancelPointer([]);\n            case 'onLostPointerCapture':\n                return (event)=>{\n                    const { internal } = store.getState();\n                    if ('pointerId' in event && internal.capturedMap.has(event.pointerId)) {\n                        // If the object event interface had onLostPointerCapture, we'd call it here on every\n                        // object that's getting removed. We call it on the next frame because onLostPointerCapture\n                        // fires before onPointerUp. Otherwise pointerUp would never be called if the event didn't\n                        // happen in the object it originated from, leaving components in a in-between state.\n                        requestAnimationFrame(()=>{\n                            // Only release if pointer-up didn't do it already\n                            if (internal.capturedMap.has(event.pointerId)) {\n                                internal.capturedMap.delete(event.pointerId);\n                                cancelPointer([]);\n                            }\n                        });\n                    }\n                };\n        }\n        // Any other pointer goes here ...\n        return function handleEvent(event) {\n            const { onPointerMissed, internal } = store.getState();\n            // prepareRay(event)\n            internal.lastEvent.current = event;\n            // Get fresh intersects\n            const isPointerMove = name === 'onPointerMove';\n            const isClickEvent = name === 'onClick' || name === 'onContextMenu' || name === 'onDoubleClick';\n            const filter = isPointerMove ? filterPointerEvents : undefined;\n            const hits = intersect(event, filter);\n            const delta = isClickEvent ? calculateDistance(event) : 0;\n            // Save initial coordinates on pointer-down\n            if (name === 'onPointerDown') {\n                internal.initialClick = [\n                    event.offsetX,\n                    event.offsetY\n                ];\n                internal.initialHits = hits.map((hit)=>hit.eventObject);\n            }\n            // If a click yields no results, pass it back to the user as a miss\n            // Missed events have to come first in order to establish user-land side-effect clean up\n            if (isClickEvent && !hits.length) {\n                if (delta <= 2) {\n                    pointerMissed(event, internal.interaction);\n                    if (onPointerMissed) onPointerMissed(event);\n                }\n            }\n            // Take care of unhover\n            if (isPointerMove) cancelPointer(hits);\n            function onIntersect(data) {\n                const eventObject = data.eventObject;\n                const instance = eventObject.__r3f;\n                // Check presence of handlers\n                if (!(instance != null && instance.eventCount)) return;\n                const handlers = instance.handlers;\n                /*\n        MAYBE TODO, DELETE IF NOT: \n          Check if the object is captured, captured events should not have intersects running in parallel\n          But wouldn't it be better to just replace capturedMap with a single entry?\n          Also, are we OK with straight up making picking up multiple objects impossible?\n          \n        const pointerId = (data as ThreeEvent<PointerEvent>).pointerId        \n        if (pointerId !== undefined) {\n          const capturedMeshSet = internal.capturedMap.get(pointerId)\n          if (capturedMeshSet) {\n            const captured = capturedMeshSet.get(eventObject)\n            if (captured && captured.localState.stopped) return\n          }\n        }*/ if (isPointerMove) {\n                    // Move event ...\n                    if (handlers.onPointerOver || handlers.onPointerEnter || handlers.onPointerOut || handlers.onPointerLeave) {\n                        // When enter or out is present take care of hover-state\n                        const id = makeId(data);\n                        const hoveredItem = internal.hovered.get(id);\n                        if (!hoveredItem) {\n                            // If the object wasn't previously hovered, book it and call its handler\n                            internal.hovered.set(id, data);\n                            handlers.onPointerOver == null ? void 0 : handlers.onPointerOver(data);\n                            handlers.onPointerEnter == null ? void 0 : handlers.onPointerEnter(data);\n                        } else if (hoveredItem.stopped) {\n                            // If the object was previously hovered and stopped, we shouldn't allow other items to proceed\n                            data.stopPropagation();\n                        }\n                    }\n                    // Call mouse move\n                    handlers.onPointerMove == null ? void 0 : handlers.onPointerMove(data);\n                } else {\n                    // All other events ...\n                    const handler = handlers[name];\n                    if (handler) {\n                        // Forward all events back to their respective handlers with the exception of click events,\n                        // which must use the initial target\n                        if (!isClickEvent || internal.initialHits.includes(eventObject)) {\n                            // Missed events have to come first\n                            pointerMissed(event, internal.interaction.filter((object)=>!internal.initialHits.includes(object)));\n                            // Now call the handler\n                            handler(data);\n                        }\n                    } else {\n                        // Trigger onPointerMissed on all elements that have pointer over/out handlers, but not click and weren't hit\n                        if (isClickEvent && internal.initialHits.includes(eventObject)) {\n                            pointerMissed(event, internal.interaction.filter((object)=>!internal.initialHits.includes(object)));\n                        }\n                    }\n                }\n            }\n            handleIntersects(hits, event, delta, onIntersect);\n        };\n    }\n    return {\n        handlePointer\n    };\n}\nconst isRenderer = (def)=>!!(def != null && def.render);\nconst context = /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst createStore = (invalidate, advance)=>{\n    const rootStore = (0,zustand_traditional__WEBPACK_IMPORTED_MODULE_7__.createWithEqualityFn)((set, get)=>{\n        const position = new three__WEBPACK_IMPORTED_MODULE_6__.Vector3();\n        const defaultTarget = new three__WEBPACK_IMPORTED_MODULE_6__.Vector3();\n        const tempTarget = new three__WEBPACK_IMPORTED_MODULE_6__.Vector3();\n        function getCurrentViewport(camera = get().camera, target = defaultTarget, size = get().size) {\n            const { width, height, top, left } = size;\n            const aspect = width / height;\n            if (target.isVector3) tempTarget.copy(target);\n            else tempTarget.set(...target);\n            const distance = camera.getWorldPosition(position).distanceTo(tempTarget);\n            if (isOrthographicCamera(camera)) {\n                return {\n                    width: width / camera.zoom,\n                    height: height / camera.zoom,\n                    top,\n                    left,\n                    factor: 1,\n                    distance,\n                    aspect\n                };\n            } else {\n                const fov = camera.fov * Math.PI / 180; // convert vertical fov to radians\n                const h = 2 * Math.tan(fov / 2) * distance; // visible height\n                const w = h * (width / height);\n                return {\n                    width: w,\n                    height: h,\n                    top,\n                    left,\n                    factor: width / w,\n                    distance,\n                    aspect\n                };\n            }\n        }\n        let performanceTimeout = undefined;\n        const setPerformanceCurrent = (current)=>set((state)=>({\n                    performance: {\n                        ...state.performance,\n                        current\n                    }\n                }));\n        const pointer = new three__WEBPACK_IMPORTED_MODULE_6__.Vector2();\n        const rootState = {\n            set,\n            get,\n            // Mock objects that have to be configured\n            gl: null,\n            camera: null,\n            raycaster: null,\n            events: {\n                priority: 1,\n                enabled: true,\n                connected: false\n            },\n            scene: null,\n            xr: null,\n            invalidate: (frames = 1)=>invalidate(get(), frames),\n            advance: (timestamp, runGlobalEffects)=>advance(timestamp, runGlobalEffects, get()),\n            legacy: false,\n            linear: false,\n            flat: false,\n            controls: null,\n            clock: new three__WEBPACK_IMPORTED_MODULE_6__.Clock(),\n            pointer,\n            mouse: pointer,\n            frameloop: 'always',\n            onPointerMissed: undefined,\n            performance: {\n                current: 1,\n                min: 0.5,\n                max: 1,\n                debounce: 200,\n                regress: ()=>{\n                    const state = get();\n                    // Clear timeout\n                    if (performanceTimeout) clearTimeout(performanceTimeout);\n                    // Set lower bound performance\n                    if (state.performance.current !== state.performance.min) setPerformanceCurrent(state.performance.min);\n                    // Go back to upper bound performance after a while unless something regresses meanwhile\n                    performanceTimeout = setTimeout(()=>setPerformanceCurrent(get().performance.max), state.performance.debounce);\n                }\n            },\n            size: {\n                width: 0,\n                height: 0,\n                top: 0,\n                left: 0\n            },\n            viewport: {\n                initialDpr: 0,\n                dpr: 0,\n                width: 0,\n                height: 0,\n                top: 0,\n                left: 0,\n                aspect: 0,\n                distance: 0,\n                factor: 0,\n                getCurrentViewport\n            },\n            setEvents: (events)=>set((state)=>({\n                        ...state,\n                        events: {\n                            ...state.events,\n                            ...events\n                        }\n                    })),\n            setSize: (width, height, top = 0, left = 0)=>{\n                const camera = get().camera;\n                const size = {\n                    width,\n                    height,\n                    top,\n                    left\n                };\n                set((state)=>({\n                        size,\n                        viewport: {\n                            ...state.viewport,\n                            ...getCurrentViewport(camera, defaultTarget, size)\n                        }\n                    }));\n            },\n            setDpr: (dpr)=>set((state)=>{\n                    const resolved = calculateDpr(dpr);\n                    return {\n                        viewport: {\n                            ...state.viewport,\n                            dpr: resolved,\n                            initialDpr: state.viewport.initialDpr || resolved\n                        }\n                    };\n                }),\n            setFrameloop: (frameloop = 'always')=>{\n                const clock = get().clock;\n                // if frameloop === \"never\" clock.elapsedTime is updated using advance(timestamp)\n                clock.stop();\n                clock.elapsedTime = 0;\n                if (frameloop !== 'never') {\n                    clock.start();\n                    clock.elapsedTime = 0;\n                }\n                set(()=>({\n                        frameloop\n                    }));\n            },\n            previousRoot: undefined,\n            internal: {\n                // Events\n                interaction: [],\n                hovered: new Map(),\n                subscribers: [],\n                initialClick: [\n                    0,\n                    0\n                ],\n                initialHits: [],\n                capturedMap: new Map(),\n                lastEvent: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createRef(),\n                // Updates\n                active: false,\n                frames: 0,\n                priority: 0,\n                subscribe: (ref, priority, store)=>{\n                    const internal = get().internal;\n                    // If this subscription was given a priority, it takes rendering into its own hands\n                    // For that reason we switch off automatic rendering and increase the manual flag\n                    // As long as this flag is positive there can be no internal rendering at all\n                    // because there could be multiple render subscriptions\n                    internal.priority = internal.priority + (priority > 0 ? 1 : 0);\n                    internal.subscribers.push({\n                        ref,\n                        priority,\n                        store\n                    });\n                    // Register subscriber and sort layers from lowest to highest, meaning,\n                    // highest priority renders last (on top of the other frames)\n                    internal.subscribers = internal.subscribers.sort((a, b)=>a.priority - b.priority);\n                    return ()=>{\n                        const internal = get().internal;\n                        if (internal != null && internal.subscribers) {\n                            // Decrease manual flag if this subscription had a priority\n                            internal.priority = internal.priority - (priority > 0 ? 1 : 0);\n                            // Remove subscriber from list\n                            internal.subscribers = internal.subscribers.filter((s)=>s.ref !== ref);\n                        }\n                    };\n                }\n            }\n        };\n        return rootState;\n    });\n    const state = rootStore.getState();\n    let oldSize = state.size;\n    let oldDpr = state.viewport.dpr;\n    let oldCamera = state.camera;\n    rootStore.subscribe(()=>{\n        const { camera, size, viewport, gl, set } = rootStore.getState();\n        // Resize camera and renderer on changes to size and pixelratio\n        if (size.width !== oldSize.width || size.height !== oldSize.height || viewport.dpr !== oldDpr) {\n            oldSize = size;\n            oldDpr = viewport.dpr;\n            // Update camera & renderer\n            updateCamera(camera, size);\n            if (viewport.dpr > 0) gl.setPixelRatio(viewport.dpr);\n            const updateStyle = typeof HTMLCanvasElement !== 'undefined' && gl.domElement instanceof HTMLCanvasElement;\n            gl.setSize(size.width, size.height, updateStyle);\n        }\n        // Update viewport once the camera changes\n        if (camera !== oldCamera) {\n            oldCamera = camera;\n            // Update viewport\n            set((state)=>({\n                    viewport: {\n                        ...state.viewport,\n                        ...state.viewport.getCurrentViewport(camera)\n                    }\n                }));\n        }\n    });\n    // Invalidate on any change\n    rootStore.subscribe((state)=>invalidate(state));\n    // Return root state\n    return rootStore;\n};\n/**\n * Exposes an object's {@link Instance}.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useInstanceHandle\n *\n * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.\n */ function useInstanceHandle(ref) {\n    const instance = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(instance, {\n        \"useInstanceHandle.useImperativeHandle\": ()=>ref.current.__r3f\n    }[\"useInstanceHandle.useImperativeHandle\"], [\n        ref\n    ]);\n    return instance;\n}\n/**\n * Returns the R3F Canvas' Zustand store. Useful for [transient updates](https://github.com/pmndrs/zustand#transient-updates-for-often-occurring-state-changes).\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usestore\n */ function useStore() {\n    const store = react__WEBPACK_IMPORTED_MODULE_0__.useContext(context);\n    if (!store) throw new Error('R3F: Hooks can only be used within the Canvas component!');\n    return store;\n}\n/**\n * Accesses R3F's internal state, containing renderer, canvas, scene, etc.\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree\n */ function useThree(selector = (state)=>state, equalityFn) {\n    return useStore()(selector, equalityFn);\n}\n/**\n * Executes a callback before render in a shared frame loop.\n * Can order effects with render priority or manually render with a positive priority.\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe\n */ function useFrame(callback, renderPriority = 0) {\n    const store = useStore();\n    const subscribe = store.getState().internal.subscribe;\n    // Memoize ref\n    const ref = useMutableCallback(callback);\n    // Subscribe on mount, unsubscribe on unmount\n    useIsomorphicLayoutEffect({\n        \"useFrame.useIsomorphicLayoutEffect\": ()=>subscribe(ref, renderPriority, store)\n    }[\"useFrame.useIsomorphicLayoutEffect\"], [\n        renderPriority,\n        subscribe,\n        store\n    ]);\n    return null;\n}\n/**\n * Returns a node graph of an object with named nodes & materials.\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph\n */ function useGraph(object) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"useGraph.useMemo\": ()=>buildGraph(object)\n    }[\"useGraph.useMemo\"], [\n        object\n    ]);\n}\nconst memoizedLoaders = new WeakMap();\nconst isConstructor$1 = (value)=>{\n    var _value$prototype;\n    return typeof value === 'function' && (value == null ? void 0 : (_value$prototype = value.prototype) == null ? void 0 : _value$prototype.constructor) === value;\n};\nfunction loadingFn(extensions, onProgress) {\n    return function(Proto, ...input) {\n        let loader;\n        // Construct and cache loader if constructor was passed\n        if (isConstructor$1(Proto)) {\n            loader = memoizedLoaders.get(Proto);\n            if (!loader) {\n                loader = new Proto();\n                memoizedLoaders.set(Proto, loader);\n            }\n        } else {\n            loader = Proto;\n        }\n        // Apply loader extensions\n        if (extensions) extensions(loader);\n        // Go through the urls and load them\n        return Promise.all(input.map((input)=>new Promise((res, reject)=>loader.load(input, (data)=>{\n                    if (isObject3D(data == null ? void 0 : data.scene)) Object.assign(data, buildGraph(data.scene));\n                    res(data);\n                }, onProgress, (error)=>reject(new Error(`Could not load ${input}: ${error == null ? void 0 : error.message}`))))));\n    };\n}\n/**\n * Synchronously loads and caches assets with a three loader.\n *\n * Note: this hook's caller must be wrapped with `React.Suspense`\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader\n */ function useLoader(loader, input, extensions, onProgress) {\n    // Use suspense to load async assets\n    const keys = Array.isArray(input) ? input : [\n        input\n    ];\n    const results = (0,suspend_react__WEBPACK_IMPORTED_MODULE_8__.suspend)(loadingFn(extensions, onProgress), [\n        loader,\n        ...keys\n    ], {\n        equal: is.equ\n    });\n    // Return the object(s)\n    return Array.isArray(input) ? results : results[0];\n}\n/**\n * Preloads an asset into cache as a side-effect.\n */ useLoader.preload = function(loader, input, extensions) {\n    const keys = Array.isArray(input) ? input : [\n        input\n    ];\n    return (0,suspend_react__WEBPACK_IMPORTED_MODULE_8__.preload)(loadingFn(extensions), [\n        loader,\n        ...keys\n    ]);\n};\n/**\n * Removes a loaded asset from cache.\n */ useLoader.clear = function(loader, input) {\n    const keys = Array.isArray(input) ? input : [\n        input\n    ];\n    return (0,suspend_react__WEBPACK_IMPORTED_MODULE_8__.clear)([\n        loader,\n        ...keys\n    ]);\n};\n// TODO: upstream to DefinitelyTyped for React 19\n// https://github.com/facebook/react/issues/28956\nfunction createReconciler(config) {\n    const reconciler = react_reconciler__WEBPACK_IMPORTED_MODULE_2___default()(config);\n    reconciler.injectIntoDevTools({\n        bundleType: typeof process !== 'undefined' && \"development\" !== 'production' ? 1 : 0,\n        rendererPackageName: '@react-three/fiber',\n        version: react__WEBPACK_IMPORTED_MODULE_0__.version\n    });\n    return reconciler;\n}\nconst NoEventPriority = 0;\n// TODO: handle constructor overloads\n// https://github.com/pmndrs/react-three-fiber/pull/2931\n// https://github.com/microsoft/TypeScript/issues/37079\nconst catalogue = {};\nconst PREFIX_REGEX = /^three(?=[A-Z])/;\nconst toPascalCase = (type)=>`${type[0].toUpperCase()}${type.slice(1)}`;\nlet i = 0;\nconst isConstructor = (object)=>typeof object === 'function';\nfunction extend(objects) {\n    if (isConstructor(objects)) {\n        const Component = `${i++}`;\n        catalogue[Component] = objects;\n        return Component;\n    } else {\n        Object.assign(catalogue, objects);\n    }\n}\nfunction validateInstance(type, props) {\n    // Get target from catalogue\n    const name = toPascalCase(type);\n    const target = catalogue[name];\n    // Validate element target\n    if (type !== 'primitive' && !target) throw new Error(`R3F: ${name} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);\n    // Validate primitives\n    if (type === 'primitive' && !props.object) throw new Error(`R3F: Primitives without 'object' are invalid!`);\n    // Throw if an object or literal was passed for args\n    if (props.args !== undefined && !Array.isArray(props.args)) throw new Error('R3F: The args prop must be an array!');\n}\nfunction createInstance(type, props, root) {\n    var _props$object;\n    // Remove three* prefix from elements if native element not present\n    type = toPascalCase(type) in catalogue ? type : type.replace(PREFIX_REGEX, '');\n    validateInstance(type, props);\n    // Regenerate the R3F instance for primitives to simulate a new object\n    if (type === 'primitive' && (_props$object = props.object) != null && _props$object.__r3f) delete props.object.__r3f;\n    return prepare(props.object, root, type, props);\n}\nfunction hideInstance(instance) {\n    if (!instance.isHidden) {\n        var _instance$parent;\n        if (instance.props.attach && (_instance$parent = instance.parent) != null && _instance$parent.object) {\n            detach(instance.parent, instance);\n        } else if (isObject3D(instance.object)) {\n            instance.object.visible = false;\n        }\n        instance.isHidden = true;\n        invalidateInstance(instance);\n    }\n}\nfunction unhideInstance(instance) {\n    if (instance.isHidden) {\n        var _instance$parent2;\n        if (instance.props.attach && (_instance$parent2 = instance.parent) != null && _instance$parent2.object) {\n            attach(instance.parent, instance);\n        } else if (isObject3D(instance.object) && instance.props.visible !== false) {\n            instance.object.visible = true;\n        }\n        instance.isHidden = false;\n        invalidateInstance(instance);\n    }\n}\n// https://github.com/facebook/react/issues/20271\n// This will make sure events and attach are only handled once when trees are complete\nfunction handleContainerEffects(parent, child, beforeChild) {\n    // Bail if tree isn't mounted or parent is not a container.\n    // This ensures that the tree is finalized and React won't discard results to Suspense\n    const state = child.root.getState();\n    if (!parent.parent && parent.object !== state.scene) return;\n    // Create & link object on first run\n    if (!child.object) {\n        var _child$props$object, _child$props$args;\n        // Get target from catalogue\n        const target = catalogue[toPascalCase(child.type)];\n        // Create object\n        child.object = (_child$props$object = child.props.object) != null ? _child$props$object : new target(...(_child$props$args = child.props.args) != null ? _child$props$args : []);\n        child.object.__r3f = child;\n    }\n    // Set initial props\n    applyProps(child.object, child.props);\n    // Append instance\n    if (child.props.attach) {\n        attach(parent, child);\n    } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n        const childIndex = parent.object.children.indexOf(beforeChild == null ? void 0 : beforeChild.object);\n        if (beforeChild && childIndex !== -1) {\n            // If the child is already in the parent's children array, move it to the new position\n            // Otherwise, just insert it at the target position\n            const existingIndex = parent.object.children.indexOf(child.object);\n            if (existingIndex !== -1) {\n                parent.object.children.splice(existingIndex, 1);\n                const adjustedIndex = existingIndex < childIndex ? childIndex - 1 : childIndex;\n                parent.object.children.splice(adjustedIndex, 0, child.object);\n            } else {\n                child.object.parent = parent.object;\n                parent.object.children.splice(childIndex, 0, child.object);\n                child.object.dispatchEvent({\n                    type: 'added'\n                });\n                parent.object.dispatchEvent({\n                    type: 'childadded',\n                    child: child.object\n                });\n            }\n        } else {\n            parent.object.add(child.object);\n        }\n    }\n    // Link subtree\n    for (const childInstance of child.children)handleContainerEffects(child, childInstance);\n    // Tree was updated, request a frame\n    invalidateInstance(child);\n}\nfunction appendChild(parent, child) {\n    if (!child) return;\n    // Link instances\n    child.parent = parent;\n    parent.children.push(child);\n    // Attach tree once complete\n    handleContainerEffects(parent, child);\n}\nfunction insertBefore(parent, child, beforeChild) {\n    if (!child || !beforeChild) return;\n    // Link instances\n    child.parent = parent;\n    const childIndex = parent.children.indexOf(beforeChild);\n    if (childIndex !== -1) parent.children.splice(childIndex, 0, child);\n    else parent.children.push(child);\n    // Attach tree once complete\n    handleContainerEffects(parent, child, beforeChild);\n}\nfunction disposeOnIdle(object) {\n    if (typeof object.dispose === 'function') {\n        const handleDispose = ()=>{\n            try {\n                object.dispose();\n            } catch  {\n            // no-op\n            }\n        };\n        // In a testing environment, cleanup immediately\n        if (typeof IS_REACT_ACT_ENVIRONMENT !== 'undefined') handleDispose();\n        else (0,scheduler__WEBPACK_IMPORTED_MODULE_3__.unstable_scheduleCallback)(scheduler__WEBPACK_IMPORTED_MODULE_3__.unstable_IdlePriority, handleDispose);\n    }\n}\nfunction removeChild(parent, child, dispose) {\n    if (!child) return;\n    // Unlink instances\n    child.parent = null;\n    const childIndex = parent.children.indexOf(child);\n    if (childIndex !== -1) parent.children.splice(childIndex, 1);\n    // Eagerly tear down tree\n    if (child.props.attach) {\n        detach(parent, child);\n    } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n        parent.object.remove(child.object);\n        removeInteractivity(findInitialRoot(child), child.object);\n    }\n    // Allow objects to bail out of unmount disposal with dispose={null}\n    const shouldDispose = child.props.dispose !== null && dispose !== false;\n    // Recursively remove instance children\n    for(let i = child.children.length - 1; i >= 0; i--){\n        const node = child.children[i];\n        removeChild(child, node, shouldDispose);\n    }\n    child.children.length = 0;\n    // Unlink instance object\n    delete child.object.__r3f;\n    // Dispose object whenever the reconciler feels like it.\n    // Never dispose of primitives because their state may be kept outside of React!\n    // In order for an object to be able to dispose it\n    //   - has a dispose method\n    //   - cannot be a <primitive object={...} />\n    //   - cannot be a THREE.Scene, because three has broken its own API\n    if (shouldDispose && child.type !== 'primitive' && child.object.type !== 'Scene') {\n        disposeOnIdle(child.object);\n    }\n    // Tree was updated, request a frame for top-level instance\n    if (dispose === undefined) invalidateInstance(child);\n}\nfunction setFiberRef(fiber, publicInstance) {\n    for (const _fiber of [\n        fiber,\n        fiber.alternate\n    ]){\n        if (_fiber !== null) {\n            if (typeof _fiber.ref === 'function') {\n                _fiber.refCleanup == null ? void 0 : _fiber.refCleanup();\n                const cleanup = _fiber.ref(publicInstance);\n                if (typeof cleanup === 'function') _fiber.refCleanup = cleanup;\n            } else if (_fiber.ref) {\n                _fiber.ref.current = publicInstance;\n            }\n        }\n    }\n}\nconst reconstructed = [];\nfunction swapInstances() {\n    // Detach instance\n    for (const [instance] of reconstructed){\n        const parent = instance.parent;\n        if (parent) {\n            if (instance.props.attach) {\n                detach(parent, instance);\n            } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n                parent.object.remove(instance.object);\n            }\n            for (const child of instance.children){\n                if (child.props.attach) {\n                    detach(instance, child);\n                } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n                    instance.object.remove(child.object);\n                }\n            }\n        }\n        // If the old instance is hidden, we need to unhide it.\n        // React assumes it can discard instances since they're pure for DOM.\n        // This isn't true for us since our lifetimes are impure and longliving.\n        // So, we manually check if an instance was hidden and unhide it.\n        if (instance.isHidden) unhideInstance(instance);\n        // Dispose of old object if able\n        if (instance.object.__r3f) delete instance.object.__r3f;\n        if (instance.type !== 'primitive') disposeOnIdle(instance.object);\n    }\n    // Update instance\n    for (const [instance, props, fiber] of reconstructed){\n        instance.props = props;\n        const parent = instance.parent;\n        if (parent) {\n            var _instance$props$objec, _instance$props$args;\n            // Get target from catalogue\n            const target = catalogue[toPascalCase(instance.type)];\n            // Create object\n            instance.object = (_instance$props$objec = instance.props.object) != null ? _instance$props$objec : new target(...(_instance$props$args = instance.props.args) != null ? _instance$props$args : []);\n            instance.object.__r3f = instance;\n            setFiberRef(fiber, instance.object);\n            // Set initial props\n            applyProps(instance.object, instance.props);\n            if (instance.props.attach) {\n                attach(parent, instance);\n            } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n                parent.object.add(instance.object);\n            }\n            for (const child of instance.children){\n                if (child.props.attach) {\n                    attach(instance, child);\n                } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n                    instance.object.add(child.object);\n                }\n            }\n            // Tree was updated, request a frame\n            invalidateInstance(instance);\n        }\n    }\n    reconstructed.length = 0;\n}\n// Don't handle text instances, make it no-op\nconst handleTextInstance = ()=>{};\nconst NO_CONTEXT = {};\nlet currentUpdatePriority = NoEventPriority;\n// https://github.com/facebook/react/blob/main/packages/react-reconciler/src/ReactFiberFlags.js\nconst NoFlags = 0;\nconst Update = 4;\nconst reconciler = /* @__PURE__ */ createReconciler({\n    isPrimaryRenderer: false,\n    warnsIfNotActing: false,\n    supportsMutation: true,\n    supportsPersistence: false,\n    supportsHydration: false,\n    createInstance,\n    removeChild,\n    appendChild,\n    appendInitialChild: appendChild,\n    insertBefore,\n    appendChildToContainer (container, child) {\n        const scene = container.getState().scene.__r3f;\n        if (!child || !scene) return;\n        appendChild(scene, child);\n    },\n    removeChildFromContainer (container, child) {\n        const scene = container.getState().scene.__r3f;\n        if (!child || !scene) return;\n        removeChild(scene, child);\n    },\n    insertInContainerBefore (container, child, beforeChild) {\n        const scene = container.getState().scene.__r3f;\n        if (!child || !beforeChild || !scene) return;\n        insertBefore(scene, child, beforeChild);\n    },\n    getRootHostContext: ()=>NO_CONTEXT,\n    getChildHostContext: ()=>NO_CONTEXT,\n    commitUpdate (instance, type, oldProps, newProps, fiber) {\n        var _newProps$args, _oldProps$args, _newProps$args2;\n        validateInstance(type, newProps);\n        let reconstruct = false;\n        // Reconstruct primitives if object prop changes\n        if (instance.type === 'primitive' && oldProps.object !== newProps.object) reconstruct = true;\n        else if (((_newProps$args = newProps.args) == null ? void 0 : _newProps$args.length) !== ((_oldProps$args = oldProps.args) == null ? void 0 : _oldProps$args.length)) reconstruct = true;\n        else if ((_newProps$args2 = newProps.args) != null && _newProps$args2.some((value, index)=>{\n            var _oldProps$args2;\n            return value !== ((_oldProps$args2 = oldProps.args) == null ? void 0 : _oldProps$args2[index]);\n        })) reconstruct = true;\n        // Reconstruct when args or <primitive object={...} have changes\n        if (reconstruct) {\n            reconstructed.push([\n                instance,\n                {\n                    ...newProps\n                },\n                fiber\n            ]);\n        } else {\n            // Create a diff-set, flag if there are any changes\n            const changedProps = diffProps(instance, newProps);\n            if (Object.keys(changedProps).length) {\n                Object.assign(instance.props, changedProps);\n                applyProps(instance.object, changedProps);\n            }\n        }\n        // Flush reconstructed siblings when we hit the last updated child in a sequence\n        const isTailSibling = fiber.sibling === null || (fiber.flags & Update) === NoFlags;\n        if (isTailSibling) swapInstances();\n    },\n    finalizeInitialChildren: ()=>false,\n    commitMount () {},\n    getPublicInstance: (instance)=>instance == null ? void 0 : instance.object,\n    prepareForCommit: ()=>null,\n    preparePortalMount: (container)=>prepare(container.getState().scene, container, '', {}),\n    resetAfterCommit: ()=>{},\n    shouldSetTextContent: ()=>false,\n    clearContainer: ()=>false,\n    hideInstance,\n    unhideInstance,\n    createTextInstance: handleTextInstance,\n    hideTextInstance: handleTextInstance,\n    unhideTextInstance: handleTextInstance,\n    scheduleTimeout: typeof setTimeout === 'function' ? setTimeout : undefined,\n    cancelTimeout: typeof clearTimeout === 'function' ? clearTimeout : undefined,\n    noTimeout: -1,\n    getInstanceFromNode: ()=>null,\n    beforeActiveInstanceBlur () {},\n    afterActiveInstanceBlur () {},\n    detachDeletedInstance () {},\n    prepareScopeUpdate () {},\n    getInstanceFromScope: ()=>null,\n    shouldAttemptEagerTransition: ()=>false,\n    trackSchedulerEvent: ()=>{},\n    resolveEventType: ()=>null,\n    resolveEventTimeStamp: ()=>-1.1,\n    requestPostPaintCallback () {},\n    maySuspendCommit: ()=>false,\n    preloadInstance: ()=>true,\n    // true indicates already loaded\n    startSuspendingCommit () {},\n    suspendInstance () {},\n    waitForCommitToBeReady: ()=>null,\n    NotPendingTransition: null,\n    HostTransitionContext: /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null),\n    setCurrentUpdatePriority (newPriority) {\n        currentUpdatePriority = newPriority;\n    },\n    getCurrentUpdatePriority () {\n        return currentUpdatePriority;\n    },\n    resolveUpdatePriority () {\n        var _window$event;\n        if (currentUpdatePriority !== NoEventPriority) return currentUpdatePriority;\n        switch( false && (0)){\n            case 'click':\n            case 'contextmenu':\n            case 'dblclick':\n            case 'pointercancel':\n            case 'pointerdown':\n            case 'pointerup':\n                return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DiscreteEventPriority;\n            case 'pointermove':\n            case 'pointerout':\n            case 'pointerover':\n            case 'pointerenter':\n            case 'pointerleave':\n            case 'wheel':\n                return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.ContinuousEventPriority;\n            default:\n                return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DefaultEventPriority;\n        }\n    },\n    resetFormInstance () {}\n});\nconst _roots = new Map();\nconst shallowLoose = {\n    objects: 'shallow',\n    strict: false\n};\nfunction computeInitialSize(canvas, size) {\n    if (!size && typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement && canvas.parentElement) {\n        const { width, height, top, left } = canvas.parentElement.getBoundingClientRect();\n        return {\n            width,\n            height,\n            top,\n            left\n        };\n    } else if (!size && typeof OffscreenCanvas !== 'undefined' && canvas instanceof OffscreenCanvas) {\n        return {\n            width: canvas.width,\n            height: canvas.height,\n            top: 0,\n            left: 0\n        };\n    }\n    return {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        ...size\n    };\n}\nfunction createRoot(canvas) {\n    // Check against mistaken use of createRoot\n    const prevRoot = _roots.get(canvas);\n    const prevFiber = prevRoot == null ? void 0 : prevRoot.fiber;\n    const prevStore = prevRoot == null ? void 0 : prevRoot.store;\n    if (prevRoot) console.warn('R3F.createRoot should only be called once!');\n    // Report when an error was detected in a previous render\n    // https://github.com/pmndrs/react-three-fiber/pull/2261\n    const logRecoverableError = typeof reportError === 'function' ? // In modern browsers, reportError will dispatch an error event,\n    // emulating an uncaught JavaScript error.\n    reportError : // In older browsers and test environments, fallback to console.error.\n    console.error;\n    // Create store\n    const store = prevStore || createStore(invalidate, advance);\n    // Create renderer\n    const fiber = prevFiber || reconciler.createContainer(store, // container\n    react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.ConcurrentRoot, // tag\n    null, // hydration callbacks\n    false, // isStrictMode\n    null, // concurrentUpdatesByDefaultOverride\n    '', // identifierPrefix\n    logRecoverableError, // onUncaughtError\n    logRecoverableError, // onCaughtError\n    logRecoverableError, // onRecoverableError\n    null // transitionCallbacks\n    );\n    // Map it\n    if (!prevRoot) _roots.set(canvas, {\n        fiber,\n        store\n    });\n    // Locals\n    let onCreated;\n    let lastCamera;\n    let configured = false;\n    let pending = null;\n    return {\n        async configure (props = {}) {\n            let resolve;\n            pending = new Promise((_resolve)=>resolve = _resolve);\n            let { gl: glConfig, size: propsSize, scene: sceneOptions, events, onCreated: onCreatedCallback, shadows = false, linear = false, flat = false, legacy = false, orthographic = false, frameloop = 'always', dpr = [\n                1,\n                2\n            ], performance, raycaster: raycastOptions, camera: cameraOptions, onPointerMissed } = props;\n            let state = store.getState();\n            // Set up renderer (one time only!)\n            let gl = state.gl;\n            if (!state.gl) {\n                const defaultProps = {\n                    canvas: canvas,\n                    powerPreference: 'high-performance',\n                    antialias: true,\n                    alpha: true\n                };\n                const customRenderer = typeof glConfig === 'function' ? await glConfig(defaultProps) : glConfig;\n                if (isRenderer(customRenderer)) {\n                    gl = customRenderer;\n                } else {\n                    gl = new three__WEBPACK_IMPORTED_MODULE_9__.WebGLRenderer({\n                        ...defaultProps,\n                        ...glConfig\n                    });\n                }\n                state.set({\n                    gl\n                });\n            }\n            // Set up raycaster (one time only!)\n            let raycaster = state.raycaster;\n            if (!raycaster) state.set({\n                raycaster: raycaster = new three__WEBPACK_IMPORTED_MODULE_6__.Raycaster()\n            });\n            // Set raycaster options\n            const { params, ...options } = raycastOptions || {};\n            if (!is.equ(options, raycaster, shallowLoose)) applyProps(raycaster, {\n                ...options\n            });\n            if (!is.equ(params, raycaster.params, shallowLoose)) applyProps(raycaster, {\n                params: {\n                    ...raycaster.params,\n                    ...params\n                }\n            });\n            // Create default camera, don't overwrite any user-set state\n            if (!state.camera || state.camera === lastCamera && !is.equ(lastCamera, cameraOptions, shallowLoose)) {\n                lastCamera = cameraOptions;\n                const isCamera = cameraOptions == null ? void 0 : cameraOptions.isCamera;\n                const camera = isCamera ? cameraOptions : orthographic ? new three__WEBPACK_IMPORTED_MODULE_6__.OrthographicCamera(0, 0, 0, 0, 0.1, 1000) : new three__WEBPACK_IMPORTED_MODULE_6__.PerspectiveCamera(75, 0, 0.1, 1000);\n                if (!isCamera) {\n                    camera.position.z = 5;\n                    if (cameraOptions) {\n                        applyProps(camera, cameraOptions);\n                        // Preserve user-defined frustum if possible\n                        // https://github.com/pmndrs/react-three-fiber/issues/3160\n                        if (!camera.manual) {\n                            if ('aspect' in cameraOptions || 'left' in cameraOptions || 'right' in cameraOptions || 'bottom' in cameraOptions || 'top' in cameraOptions) {\n                                camera.manual = true;\n                                camera.updateProjectionMatrix();\n                            }\n                        }\n                    }\n                    // Always look at center by default\n                    if (!state.camera && !(cameraOptions != null && cameraOptions.rotation)) camera.lookAt(0, 0, 0);\n                }\n                state.set({\n                    camera\n                });\n                // Configure raycaster\n                // https://github.com/pmndrs/react-xr/issues/300\n                raycaster.camera = camera;\n            }\n            // Set up scene (one time only!)\n            if (!state.scene) {\n                let scene;\n                if (sceneOptions != null && sceneOptions.isScene) {\n                    scene = sceneOptions;\n                    prepare(scene, store, '', {});\n                } else {\n                    scene = new three__WEBPACK_IMPORTED_MODULE_6__.Scene();\n                    prepare(scene, store, '', {});\n                    if (sceneOptions) applyProps(scene, sceneOptions);\n                }\n                state.set({\n                    scene\n                });\n            }\n            // Store events internally\n            if (events && !state.events.handlers) state.set({\n                events: events(store)\n            });\n            // Check size, allow it to take on container bounds initially\n            const size = computeInitialSize(canvas, propsSize);\n            if (!is.equ(size, state.size, shallowLoose)) {\n                state.setSize(size.width, size.height, size.top, size.left);\n            }\n            // Check pixelratio\n            if (dpr && state.viewport.dpr !== calculateDpr(dpr)) state.setDpr(dpr);\n            // Check frameloop\n            if (state.frameloop !== frameloop) state.setFrameloop(frameloop);\n            // Check pointer missed\n            if (!state.onPointerMissed) state.set({\n                onPointerMissed\n            });\n            // Check performance\n            if (performance && !is.equ(performance, state.performance, shallowLoose)) state.set((state)=>({\n                    performance: {\n                        ...state.performance,\n                        ...performance\n                    }\n                }));\n            // Set up XR (one time only!)\n            if (!state.xr) {\n                var _gl$xr;\n                // Handle frame behavior in WebXR\n                const handleXRFrame = (timestamp, frame)=>{\n                    const state = store.getState();\n                    if (state.frameloop === 'never') return;\n                    advance(timestamp, true, state, frame);\n                };\n                // Toggle render switching on session\n                const handleSessionChange = ()=>{\n                    const state = store.getState();\n                    state.gl.xr.enabled = state.gl.xr.isPresenting;\n                    state.gl.xr.setAnimationLoop(state.gl.xr.isPresenting ? handleXRFrame : null);\n                    if (!state.gl.xr.isPresenting) invalidate(state);\n                };\n                // WebXR session manager\n                const xr = {\n                    connect () {\n                        const gl = store.getState().gl;\n                        gl.xr.addEventListener('sessionstart', handleSessionChange);\n                        gl.xr.addEventListener('sessionend', handleSessionChange);\n                    },\n                    disconnect () {\n                        const gl = store.getState().gl;\n                        gl.xr.removeEventListener('sessionstart', handleSessionChange);\n                        gl.xr.removeEventListener('sessionend', handleSessionChange);\n                    }\n                };\n                // Subscribe to WebXR session events\n                if (typeof ((_gl$xr = gl.xr) == null ? void 0 : _gl$xr.addEventListener) === 'function') xr.connect();\n                state.set({\n                    xr\n                });\n            }\n            // Set shadowmap\n            if (gl.shadowMap) {\n                const oldEnabled = gl.shadowMap.enabled;\n                const oldType = gl.shadowMap.type;\n                gl.shadowMap.enabled = !!shadows;\n                if (is.boo(shadows)) {\n                    gl.shadowMap.type = three__WEBPACK_IMPORTED_MODULE_6__.PCFSoftShadowMap;\n                } else if (is.str(shadows)) {\n                    var _types$shadows;\n                    const types = {\n                        basic: three__WEBPACK_IMPORTED_MODULE_6__.BasicShadowMap,\n                        percentage: three__WEBPACK_IMPORTED_MODULE_6__.PCFShadowMap,\n                        soft: three__WEBPACK_IMPORTED_MODULE_6__.PCFSoftShadowMap,\n                        variance: three__WEBPACK_IMPORTED_MODULE_6__.VSMShadowMap\n                    };\n                    gl.shadowMap.type = (_types$shadows = types[shadows]) != null ? _types$shadows : three__WEBPACK_IMPORTED_MODULE_6__.PCFSoftShadowMap;\n                } else if (is.obj(shadows)) {\n                    Object.assign(gl.shadowMap, shadows);\n                }\n                if (oldEnabled !== gl.shadowMap.enabled || oldType !== gl.shadowMap.type) gl.shadowMap.needsUpdate = true;\n            }\n            three__WEBPACK_IMPORTED_MODULE_6__.ColorManagement.enabled = !legacy;\n            // Set color space and tonemapping preferences\n            if (!configured) {\n                gl.outputColorSpace = linear ? three__WEBPACK_IMPORTED_MODULE_6__.LinearSRGBColorSpace : three__WEBPACK_IMPORTED_MODULE_6__.SRGBColorSpace;\n                gl.toneMapping = flat ? three__WEBPACK_IMPORTED_MODULE_6__.NoToneMapping : three__WEBPACK_IMPORTED_MODULE_6__.ACESFilmicToneMapping;\n            }\n            // Update color management state\n            if (state.legacy !== legacy) state.set(()=>({\n                    legacy\n                }));\n            if (state.linear !== linear) state.set(()=>({\n                    linear\n                }));\n            if (state.flat !== flat) state.set(()=>({\n                    flat\n                }));\n            // Set gl props\n            if (glConfig && !is.fun(glConfig) && !isRenderer(glConfig) && !is.equ(glConfig, gl, shallowLoose)) applyProps(gl, glConfig);\n            // Set locals\n            onCreated = onCreatedCallback;\n            configured = true;\n            resolve();\n            return this;\n        },\n        render (children) {\n            // The root has to be configured before it can be rendered\n            if (!configured && !pending) this.configure();\n            pending.then(()=>{\n                reconciler.updateContainer(/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Provider, {\n                    store: store,\n                    children: children,\n                    onCreated: onCreated,\n                    rootElement: canvas\n                }), fiber, null, ()=>undefined);\n            });\n            return store;\n        },\n        unmount () {\n            unmountComponentAtNode(canvas);\n        }\n    };\n}\nfunction Provider({ store, children, onCreated, rootElement }) {\n    useIsomorphicLayoutEffect({\n        \"Provider.useIsomorphicLayoutEffect\": ()=>{\n            const state = store.getState();\n            // Flag the canvas active, rendering will now begin\n            state.set({\n                \"Provider.useIsomorphicLayoutEffect\": (state)=>({\n                        internal: {\n                            ...state.internal,\n                            active: true\n                        }\n                    })\n            }[\"Provider.useIsomorphicLayoutEffect\"]);\n            // Notify that init is completed, the scene graph exists, but nothing has yet rendered\n            if (onCreated) onCreated(state);\n            // Connect events to the targets parent, this is done to ensure events are registered on\n            // a shared target, and not on the canvas itself\n            if (!store.getState().events.connected) state.events.connect == null ? void 0 : state.events.connect(rootElement);\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"Provider.useIsomorphicLayoutEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(context.Provider, {\n        value: store,\n        children: children\n    });\n}\nfunction unmountComponentAtNode(canvas, callback) {\n    const root = _roots.get(canvas);\n    const fiber = root == null ? void 0 : root.fiber;\n    if (fiber) {\n        const state = root == null ? void 0 : root.store.getState();\n        if (state) state.internal.active = false;\n        reconciler.updateContainer(null, fiber, null, ()=>{\n            if (state) {\n                setTimeout(()=>{\n                    try {\n                        var _state$gl, _state$gl$renderLists, _state$gl2, _state$gl3;\n                        state.events.disconnect == null ? void 0 : state.events.disconnect();\n                        (_state$gl = state.gl) == null ? void 0 : (_state$gl$renderLists = _state$gl.renderLists) == null ? void 0 : _state$gl$renderLists.dispose == null ? void 0 : _state$gl$renderLists.dispose();\n                        (_state$gl2 = state.gl) == null ? void 0 : _state$gl2.forceContextLoss == null ? void 0 : _state$gl2.forceContextLoss();\n                        if ((_state$gl3 = state.gl) != null && _state$gl3.xr) state.xr.disconnect();\n                        dispose(state.scene);\n                        _roots.delete(canvas);\n                        if (callback) callback(canvas);\n                    } catch (e) {\n                    /* ... */ }\n                }, 500);\n            }\n        });\n    }\n}\nfunction createPortal(children, container, state) {\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Portal, {\n        children: children,\n        container: container,\n        state: state\n    });\n}\nfunction Portal({ state = {}, children, container }) {\n    /** This has to be a component because it would not be able to call useThree/useStore otherwise since\n   *  if this is our environment, then we are not in r3f's renderer but in react-dom, it would trigger\n   *  the \"R3F hooks can only be used within the Canvas component!\" warning:\n   *  <Canvas>\n   *    {createPortal(...)} */ const { events, size, ...rest } = state;\n    const previousRoot = useStore();\n    const [raycaster] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"Portal.useState\": ()=>new three__WEBPACK_IMPORTED_MODULE_6__.Raycaster()\n    }[\"Portal.useState\"]);\n    const [pointer] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"Portal.useState\": ()=>new three__WEBPACK_IMPORTED_MODULE_6__.Vector2()\n    }[\"Portal.useState\"]);\n    const inject = useMutableCallback({\n        \"Portal.useMutableCallback[inject]\": (rootState, injectState)=>{\n            let viewport = undefined;\n            if (injectState.camera && size) {\n                const camera = injectState.camera;\n                // Calculate the override viewport, if present\n                viewport = rootState.viewport.getCurrentViewport(camera, new three__WEBPACK_IMPORTED_MODULE_6__.Vector3(), size);\n                // Update the portal camera, if it differs from the previous layer\n                if (camera !== rootState.camera) updateCamera(camera, size);\n            }\n            return {\n                // The intersect consists of the previous root state\n                ...rootState,\n                ...injectState,\n                // Portals have their own scene, which forms the root, a raycaster and a pointer\n                scene: container,\n                raycaster,\n                pointer,\n                mouse: pointer,\n                // Their previous root is the layer before it\n                previousRoot,\n                // Events, size and viewport can be overridden by the inject layer\n                events: {\n                    ...rootState.events,\n                    ...injectState.events,\n                    ...events\n                },\n                size: {\n                    ...rootState.size,\n                    ...size\n                },\n                viewport: {\n                    ...rootState.viewport,\n                    ...viewport\n                },\n                // Layers are allowed to override events\n                setEvents: ({\n                    \"Portal.useMutableCallback[inject]\": (events)=>injectState.set({\n                            \"Portal.useMutableCallback[inject]\": (state)=>({\n                                    ...state,\n                                    events: {\n                                        ...state.events,\n                                        ...events\n                                    }\n                                })\n                        }[\"Portal.useMutableCallback[inject]\"])\n                })[\"Portal.useMutableCallback[inject]\"]\n            };\n        }\n    }[\"Portal.useMutableCallback[inject]\"]);\n    const usePortalStore = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Portal.useMemo[usePortalStore]\": ()=>{\n            // Create a mirrored store, based on the previous root with a few overrides ...\n            const store = (0,zustand_traditional__WEBPACK_IMPORTED_MODULE_7__.createWithEqualityFn)({\n                \"Portal.useMemo[usePortalStore].store\": (set, get)=>({\n                        ...rest,\n                        set,\n                        get\n                    })\n            }[\"Portal.useMemo[usePortalStore].store\"]);\n            // Subscribe to previous root-state and copy changes over to the mirrored portal-state\n            const onMutate = {\n                \"Portal.useMemo[usePortalStore].onMutate\": (prev)=>store.setState({\n                        \"Portal.useMemo[usePortalStore].onMutate\": (state)=>inject.current(prev, state)\n                    }[\"Portal.useMemo[usePortalStore].onMutate\"])\n            }[\"Portal.useMemo[usePortalStore].onMutate\"];\n            onMutate(previousRoot.getState());\n            previousRoot.subscribe(onMutate);\n            return store;\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"Portal.useMemo[usePortalStore]\"], [\n        previousRoot,\n        container\n    ]);\n    return(/*#__PURE__*/ // @ts-ignore, reconciler types are not maintained\n    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment, {\n        children: reconciler.createPortal(/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(context.Provider, {\n            value: usePortalStore,\n            children: children\n        }), usePortalStore, null)\n    }));\n}\n/**\n * Force React to flush any updates inside the provided callback synchronously and immediately.\n * All the same caveats documented for react-dom's `flushSync` apply here (see https://react.dev/reference/react-dom/flushSync).\n * Nevertheless, sometimes one needs to render synchronously, for example to keep DOM and 3D changes in lock-step without\n * having to revert to a non-React solution.\n */ function flushSync(fn) {\n    return reconciler.flushSync(fn);\n}\nfunction createSubs(callback, subs) {\n    const sub = {\n        callback\n    };\n    subs.add(sub);\n    return ()=>void subs.delete(sub);\n}\nconst globalEffects = new Set();\nconst globalAfterEffects = new Set();\nconst globalTailEffects = new Set();\n/**\n * Adds a global render callback which is called each frame.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addEffect\n */ const addEffect = (callback)=>createSubs(callback, globalEffects);\n/**\n * Adds a global after-render callback which is called each frame.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addAfterEffect\n */ const addAfterEffect = (callback)=>createSubs(callback, globalAfterEffects);\n/**\n * Adds a global callback which is called when rendering stops.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addTail\n */ const addTail = (callback)=>createSubs(callback, globalTailEffects);\nfunction run(effects, timestamp) {\n    if (!effects.size) return;\n    for (const { callback } of effects.values()){\n        callback(timestamp);\n    }\n}\nfunction flushGlobalEffects(type, timestamp) {\n    switch(type){\n        case 'before':\n            return run(globalEffects, timestamp);\n        case 'after':\n            return run(globalAfterEffects, timestamp);\n        case 'tail':\n            return run(globalTailEffects, timestamp);\n    }\n}\nlet subscribers;\nlet subscription;\nfunction update(timestamp, state, frame) {\n    // Run local effects\n    let delta = state.clock.getDelta();\n    // In frameloop='never' mode, clock times are updated using the provided timestamp\n    if (state.frameloop === 'never' && typeof timestamp === 'number') {\n        delta = timestamp - state.clock.elapsedTime;\n        state.clock.oldTime = state.clock.elapsedTime;\n        state.clock.elapsedTime = timestamp;\n    }\n    // Call subscribers (useFrame)\n    subscribers = state.internal.subscribers;\n    for(let i = 0; i < subscribers.length; i++){\n        subscription = subscribers[i];\n        subscription.ref.current(subscription.store.getState(), delta, frame);\n    }\n    // Render content\n    if (!state.internal.priority && state.gl.render) state.gl.render(state.scene, state.camera);\n    // Decrease frame count\n    state.internal.frames = Math.max(0, state.internal.frames - 1);\n    return state.frameloop === 'always' ? 1 : state.internal.frames;\n}\nlet running = false;\nlet useFrameInProgress = false;\nlet repeat;\nlet frame;\nlet state;\nfunction loop(timestamp) {\n    frame = requestAnimationFrame(loop);\n    running = true;\n    repeat = 0;\n    // Run effects\n    flushGlobalEffects('before', timestamp);\n    // Render all roots\n    useFrameInProgress = true;\n    for (const root of _roots.values()){\n        var _state$gl$xr;\n        state = root.store.getState();\n        // If the frameloop is invalidated, do not run another frame\n        if (state.internal.active && (state.frameloop === 'always' || state.internal.frames > 0) && !((_state$gl$xr = state.gl.xr) != null && _state$gl$xr.isPresenting)) {\n            repeat += update(timestamp, state);\n        }\n    }\n    useFrameInProgress = false;\n    // Run after-effects\n    flushGlobalEffects('after', timestamp);\n    // Stop the loop if nothing invalidates it\n    if (repeat === 0) {\n        // Tail call effects, they are called when rendering stops\n        flushGlobalEffects('tail', timestamp);\n        // Flag end of operation\n        running = false;\n        return cancelAnimationFrame(frame);\n    }\n}\n/**\n * Invalidates the view, requesting a frame to be rendered. Will globally invalidate unless passed a root's state.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#invalidate\n */ function invalidate(state, frames = 1) {\n    var _state$gl$xr2;\n    if (!state) return _roots.forEach((root)=>invalidate(root.store.getState(), frames));\n    if ((_state$gl$xr2 = state.gl.xr) != null && _state$gl$xr2.isPresenting || !state.internal.active || state.frameloop === 'never') return;\n    if (frames > 1) {\n        // legacy support for people using frames parameters\n        // Increase frames, do not go higher than 60\n        state.internal.frames = Math.min(60, state.internal.frames + frames);\n    } else {\n        if (useFrameInProgress) {\n            //called from within a useFrame, it means the user wants an additional frame\n            state.internal.frames = 2;\n        } else {\n            //the user need a new frame, no need to increment further than 1\n            state.internal.frames = 1;\n        }\n    }\n    // If the render-loop isn't active, start it\n    if (!running) {\n        running = true;\n        requestAnimationFrame(loop);\n    }\n}\n/**\n * Advances the frameloop and runs render effects, useful for when manually rendering via `frameloop=\"never\"`.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#advance\n */ function advance(timestamp, runGlobalEffects = true, state, frame) {\n    if (runGlobalEffects) flushGlobalEffects('before', timestamp);\n    if (!state) for (const root of _roots.values())update(timestamp, root.store.getState());\n    else update(timestamp, state, frame);\n    if (runGlobalEffects) flushGlobalEffects('after', timestamp);\n}\nconst DOM_EVENTS = {\n    onClick: [\n        'click',\n        false\n    ],\n    onContextMenu: [\n        'contextmenu',\n        false\n    ],\n    onDoubleClick: [\n        'dblclick',\n        false\n    ],\n    onWheel: [\n        'wheel',\n        true\n    ],\n    onPointerDown: [\n        'pointerdown',\n        true\n    ],\n    onPointerUp: [\n        'pointerup',\n        true\n    ],\n    onPointerLeave: [\n        'pointerleave',\n        true\n    ],\n    onPointerMove: [\n        'pointermove',\n        true\n    ],\n    onPointerCancel: [\n        'pointercancel',\n        true\n    ],\n    onLostPointerCapture: [\n        'lostpointercapture',\n        true\n    ]\n};\n/** Default R3F event manager for web */ function createPointerEvents(store) {\n    const { handlePointer } = createEvents(store);\n    return {\n        priority: 1,\n        enabled: true,\n        compute (event, state, previous) {\n            // https://github.com/pmndrs/react-three-fiber/pull/782\n            // Events trigger outside of canvas when moved, use offsetX/Y by default and allow overrides\n            state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n            state.raycaster.setFromCamera(state.pointer, state.camera);\n        },\n        connected: undefined,\n        handlers: Object.keys(DOM_EVENTS).reduce((acc, key)=>({\n                ...acc,\n                [key]: handlePointer(key)\n            }), {}),\n        update: ()=>{\n            var _internal$lastEvent;\n            const { events, internal } = store.getState();\n            if ((_internal$lastEvent = internal.lastEvent) != null && _internal$lastEvent.current && events.handlers) events.handlers.onPointerMove(internal.lastEvent.current);\n        },\n        connect: (target)=>{\n            const { set, events } = store.getState();\n            events.disconnect == null ? void 0 : events.disconnect();\n            set((state)=>({\n                    events: {\n                        ...state.events,\n                        connected: target\n                    }\n                }));\n            if (events.handlers) {\n                for(const name in events.handlers){\n                    const event = events.handlers[name];\n                    const [eventName, passive] = DOM_EVENTS[name];\n                    target.addEventListener(eventName, event, {\n                        passive\n                    });\n                }\n            }\n        },\n        disconnect: ()=>{\n            const { set, events } = store.getState();\n            if (events.connected) {\n                if (events.handlers) {\n                    for(const name in events.handlers){\n                        const event = events.handlers[name];\n                        const [eventName] = DOM_EVENTS[name];\n                        events.connected.removeEventListener(eventName, event);\n                    }\n                }\n                set((state)=>({\n                        events: {\n                            ...state.events,\n                            connected: undefined\n                        }\n                    }));\n            }\n        }\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/dist/events-cf57b220.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Canvas: () => (/* binding */ Canvas),\n/* harmony export */   ReactThreeFiber: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.t),\n/* harmony export */   _roots: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__._),\n/* harmony export */   act: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.x),\n/* harmony export */   addAfterEffect: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.k),\n/* harmony export */   addEffect: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   addTail: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.l),\n/* harmony export */   advance: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   applyProps: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.s),\n/* harmony export */   buildGraph: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.y),\n/* harmony export */   context: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.q),\n/* harmony export */   createEvents: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.g),\n/* harmony export */   createPortal: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.o),\n/* harmony export */   createRoot: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   dispose: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.w),\n/* harmony export */   events: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   extend: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.e),\n/* harmony export */   flushGlobalEffects: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.h),\n/* harmony export */   flushSync: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   getRootState: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.v),\n/* harmony export */   invalidate: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.m),\n/* harmony export */   reconciler: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.r),\n/* harmony export */   unmountComponentAtNode: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   useFrame: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.D),\n/* harmony export */   useGraph: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.F),\n/* harmony export */   useInstanceHandle: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.z),\n/* harmony export */   useLoader: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.G),\n/* harmony export */   useStore: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.A),\n/* harmony export */   useThree: () => (/* reexport safe */ _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.C)\n/* harmony export */ });\n/* harmony import */ var _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./events-cf57b220.esm.js */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-cf57b220.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var react_use_measure__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-use-measure */ \"(ssr)/./node_modules/react-use-measure/dist/index.js\");\n/* harmony import */ var its_fine__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! its-fine */ \"(ssr)/./node_modules/its-fine/dist/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_reconciler_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-reconciler/constants */ \"(ssr)/./node_modules/react-reconciler/constants.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-reconciler */ \"(ssr)/./node_modules/react-reconciler/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CanvasImpl({ ref, children, fallback, resize, style, gl, events = _events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.f, eventSource, eventPrefix, shadows, linear, flat, legacy, orthographic, frameloop, dpr, performance, raycaster, camera, scene, onPointerMissed, onCreated, ...props }) {\n    // Create a known catalogue of Threejs-native elements\n    // This will include the entire THREE namespace by default, users can extend\n    // their own elements by using the createRoot API instead\n    react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"CanvasImpl.useMemo\": ()=>(0,_events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.e)(three__WEBPACK_IMPORTED_MODULE_6__)\n    }[\"CanvasImpl.useMemo\"], []);\n    const Bridge = (0,_events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.u)();\n    const [containerRef, containerRect] = (0,react_use_measure__WEBPACK_IMPORTED_MODULE_7__[\"default\"])({\n        scroll: true,\n        debounce: {\n            scroll: 50,\n            resize: 0\n        },\n        ...resize\n    });\n    const canvasRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    const divRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(ref, {\n        \"CanvasImpl.useImperativeHandle\": ()=>canvasRef.current\n    }[\"CanvasImpl.useImperativeHandle\"]);\n    const handlePointerMissed = (0,_events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)(onPointerMissed);\n    const [block, setBlock] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const [error, setError] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    // Suspend this component if block is a promise (2nd run)\n    if (block) throw block;\n    // Throw exception outwards if anything within canvas throws\n    if (error) throw error;\n    const root = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    (0,_events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.b)({\n        \"CanvasImpl.useIsomorphicLayoutEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (containerRect.width > 0 && containerRect.height > 0 && canvas) {\n                if (!root.current) root.current = (0,_events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.c)(canvas);\n                async function run() {\n                    await root.current.configure({\n                        gl,\n                        scene,\n                        events,\n                        shadows,\n                        linear,\n                        flat,\n                        legacy,\n                        orthographic,\n                        frameloop,\n                        dpr,\n                        performance,\n                        raycaster,\n                        camera,\n                        size: containerRect,\n                        // Pass mutable reference to onPointerMissed so it's free to update\n                        onPointerMissed: {\n                            \"CanvasImpl.useIsomorphicLayoutEffect.run\": (...args)=>handlePointerMissed.current == null ? void 0 : handlePointerMissed.current(...args)\n                        }[\"CanvasImpl.useIsomorphicLayoutEffect.run\"],\n                        onCreated: {\n                            \"CanvasImpl.useIsomorphicLayoutEffect.run\": (state)=>{\n                                // Connect to event source\n                                state.events.connect == null ? void 0 : state.events.connect(eventSource ? (0,_events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.i)(eventSource) ? eventSource.current : eventSource : divRef.current);\n                                // Set up compute function\n                                if (eventPrefix) {\n                                    state.setEvents({\n                                        compute: {\n                                            \"CanvasImpl.useIsomorphicLayoutEffect.run\": (event, state)=>{\n                                                const x = event[eventPrefix + 'X'];\n                                                const y = event[eventPrefix + 'Y'];\n                                                state.pointer.set(x / state.size.width * 2 - 1, -(y / state.size.height) * 2 + 1);\n                                                state.raycaster.setFromCamera(state.pointer, state.camera);\n                                            }\n                                        }[\"CanvasImpl.useIsomorphicLayoutEffect.run\"]\n                                    });\n                                }\n                                // Call onCreated callback\n                                onCreated == null ? void 0 : onCreated(state);\n                            }\n                        }[\"CanvasImpl.useIsomorphicLayoutEffect.run\"]\n                    });\n                    root.current.render(/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Bridge, {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.E, {\n                            set: setError,\n                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                                fallback: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.B, {\n                                    set: setBlock\n                                }),\n                                children: children != null ? children : null\n                            })\n                        })\n                    }));\n                }\n                run();\n            }\n        }\n    }[\"CanvasImpl.useIsomorphicLayoutEffect\"]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"CanvasImpl.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (canvas) return ({\n                \"CanvasImpl.useEffect\": ()=>(0,_events_cf57b220_esm_js__WEBPACK_IMPORTED_MODULE_0__.d)(canvas)\n            })[\"CanvasImpl.useEffect\"];\n        }\n    }[\"CanvasImpl.useEffect\"], []);\n    // When the event source is not this div, we need to set pointer-events to none\n    // Or else the canvas will block events from reaching the event source\n    const pointerEvents = eventSource ? 'none' : 'auto';\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n        ref: divRef,\n        style: {\n            position: 'relative',\n            width: '100%',\n            height: '100%',\n            overflow: 'hidden',\n            pointerEvents,\n            ...style\n        },\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n            ref: containerRef,\n            style: {\n                width: '100%',\n                height: '100%'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"canvas\", {\n                ref: canvasRef,\n                style: {\n                    display: 'block'\n                },\n                children: fallback\n            })\n        })\n    });\n}\n/**\n * A DOM canvas which accepts threejs elements as children.\n * @see https://docs.pmnd.rs/react-three-fiber/api/canvas\n */ function Canvas(props) {\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(its_fine__WEBPACK_IMPORTED_MODULE_8__.FiberProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(CanvasImpl, {\n            ...props\n        })\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ \n true && function() {\n    function performWorkUntilDeadline() {\n        if (isMessageLoopRunning) {\n            var currentTime = exports.unstable_now();\n            startTime = currentTime;\n            var hasMoreWork = !0;\n            try {\n                a: {\n                    isHostCallbackScheduled = !1;\n                    isHostTimeoutScheduled && (isHostTimeoutScheduled = !1, localClearTimeout(taskTimeoutID), taskTimeoutID = -1);\n                    isPerformingWork = !0;\n                    var previousPriorityLevel = currentPriorityLevel;\n                    try {\n                        b: {\n                            advanceTimers(currentTime);\n                            for(currentTask = peek(taskQueue); null !== currentTask && !(currentTask.expirationTime > currentTime && shouldYieldToHost());){\n                                var callback = currentTask.callback;\n                                if (\"function\" === typeof callback) {\n                                    currentTask.callback = null;\n                                    currentPriorityLevel = currentTask.priorityLevel;\n                                    var continuationCallback = callback(currentTask.expirationTime <= currentTime);\n                                    currentTime = exports.unstable_now();\n                                    if (\"function\" === typeof continuationCallback) {\n                                        currentTask.callback = continuationCallback;\n                                        advanceTimers(currentTime);\n                                        hasMoreWork = !0;\n                                        break b;\n                                    }\n                                    currentTask === peek(taskQueue) && pop(taskQueue);\n                                    advanceTimers(currentTime);\n                                } else pop(taskQueue);\n                                currentTask = peek(taskQueue);\n                            }\n                            if (null !== currentTask) hasMoreWork = !0;\n                            else {\n                                var firstTimer = peek(timerQueue);\n                                null !== firstTimer && requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n                                hasMoreWork = !1;\n                            }\n                        }\n                        break a;\n                    } finally{\n                        currentTask = null, currentPriorityLevel = previousPriorityLevel, isPerformingWork = !1;\n                    }\n                    hasMoreWork = void 0;\n                }\n            } finally{\n                hasMoreWork ? schedulePerformWorkUntilDeadline() : isMessageLoopRunning = !1;\n            }\n        }\n    }\n    function push(heap, node) {\n        var index = heap.length;\n        heap.push(node);\n        a: for(; 0 < index;){\n            var parentIndex = index - 1 >>> 1, parent = heap[parentIndex];\n            if (0 < compare(parent, node)) heap[parentIndex] = node, heap[index] = parent, index = parentIndex;\n            else break a;\n        }\n    }\n    function peek(heap) {\n        return 0 === heap.length ? null : heap[0];\n    }\n    function pop(heap) {\n        if (0 === heap.length) return null;\n        var first = heap[0], last = heap.pop();\n        if (last !== first) {\n            heap[0] = last;\n            a: for(var index = 0, length = heap.length, halfLength = length >>> 1; index < halfLength;){\n                var leftIndex = 2 * (index + 1) - 1, left = heap[leftIndex], rightIndex = leftIndex + 1, right = heap[rightIndex];\n                if (0 > compare(left, last)) rightIndex < length && 0 > compare(right, left) ? (heap[index] = right, heap[rightIndex] = last, index = rightIndex) : (heap[index] = left, heap[leftIndex] = last, index = leftIndex);\n                else if (rightIndex < length && 0 > compare(right, last)) heap[index] = right, heap[rightIndex] = last, index = rightIndex;\n                else break a;\n            }\n        }\n        return first;\n    }\n    function compare(a, b) {\n        var diff = a.sortIndex - b.sortIndex;\n        return 0 !== diff ? diff : a.id - b.id;\n    }\n    function advanceTimers(currentTime) {\n        for(var timer = peek(timerQueue); null !== timer;){\n            if (null === timer.callback) pop(timerQueue);\n            else if (timer.startTime <= currentTime) pop(timerQueue), timer.sortIndex = timer.expirationTime, push(taskQueue, timer);\n            else break;\n            timer = peek(timerQueue);\n        }\n    }\n    function handleTimeout(currentTime) {\n        isHostTimeoutScheduled = !1;\n        advanceTimers(currentTime);\n        if (!isHostCallbackScheduled) if (null !== peek(taskQueue)) isHostCallbackScheduled = !0, requestHostCallback();\n        else {\n            var firstTimer = peek(timerQueue);\n            null !== firstTimer && requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n        }\n    }\n    function shouldYieldToHost() {\n        return exports.unstable_now() - startTime < frameInterval ? !1 : !0;\n    }\n    function requestHostCallback() {\n        isMessageLoopRunning || (isMessageLoopRunning = !0, schedulePerformWorkUntilDeadline());\n    }\n    function requestHostTimeout(callback, ms) {\n        taskTimeoutID = localSetTimeout(function() {\n            callback(exports.unstable_now());\n        }, ms);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    exports.unstable_now = void 0;\n    if (\"object\" === typeof performance && \"function\" === typeof performance.now) {\n        var localPerformance = performance;\n        exports.unstable_now = function() {\n            return localPerformance.now();\n        };\n    } else {\n        var localDate = Date, initialTime = localDate.now();\n        exports.unstable_now = function() {\n            return localDate.now() - initialTime;\n        };\n    }\n    var taskQueue = [], timerQueue = [], taskIdCounter = 1, currentTask = null, currentPriorityLevel = 3, isPerformingWork = !1, isHostCallbackScheduled = !1, isHostTimeoutScheduled = !1, localSetTimeout = \"function\" === typeof setTimeout ? setTimeout : null, localClearTimeout = \"function\" === typeof clearTimeout ? clearTimeout : null, localSetImmediate = \"undefined\" !== typeof setImmediate ? setImmediate : null, isMessageLoopRunning = !1, taskTimeoutID = -1, frameInterval = 5, startTime = -1;\n    if (\"function\" === typeof localSetImmediate) var schedulePerformWorkUntilDeadline = function() {\n        localSetImmediate(performWorkUntilDeadline);\n    };\n    else if (\"undefined\" !== typeof MessageChannel) {\n        var channel = new MessageChannel(), port = channel.port2;\n        channel.port1.onmessage = performWorkUntilDeadline;\n        schedulePerformWorkUntilDeadline = function() {\n            port.postMessage(null);\n        };\n    } else schedulePerformWorkUntilDeadline = function() {\n        localSetTimeout(performWorkUntilDeadline, 0);\n    };\n    exports.unstable_IdlePriority = 5;\n    exports.unstable_ImmediatePriority = 1;\n    exports.unstable_LowPriority = 4;\n    exports.unstable_NormalPriority = 3;\n    exports.unstable_Profiling = null;\n    exports.unstable_UserBlockingPriority = 2;\n    exports.unstable_cancelCallback = function(task) {\n        task.callback = null;\n    };\n    exports.unstable_continueExecution = function() {\n        isHostCallbackScheduled || isPerformingWork || (isHostCallbackScheduled = !0, requestHostCallback());\n    };\n    exports.unstable_forceFrameRate = function(fps) {\n        0 > fps || 125 < fps ? console.error(\"forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported\") : frameInterval = 0 < fps ? Math.floor(1e3 / fps) : 5;\n    };\n    exports.unstable_getCurrentPriorityLevel = function() {\n        return currentPriorityLevel;\n    };\n    exports.unstable_getFirstCallbackNode = function() {\n        return peek(taskQueue);\n    };\n    exports.unstable_next = function(eventHandler) {\n        switch(currentPriorityLevel){\n            case 1:\n            case 2:\n            case 3:\n                var priorityLevel = 3;\n                break;\n            default:\n                priorityLevel = currentPriorityLevel;\n        }\n        var previousPriorityLevel = currentPriorityLevel;\n        currentPriorityLevel = priorityLevel;\n        try {\n            return eventHandler();\n        } finally{\n            currentPriorityLevel = previousPriorityLevel;\n        }\n    };\n    exports.unstable_pauseExecution = function() {};\n    exports.unstable_requestPaint = function() {};\n    exports.unstable_runWithPriority = function(priorityLevel, eventHandler) {\n        switch(priorityLevel){\n            case 1:\n            case 2:\n            case 3:\n            case 4:\n            case 5:\n                break;\n            default:\n                priorityLevel = 3;\n        }\n        var previousPriorityLevel = currentPriorityLevel;\n        currentPriorityLevel = priorityLevel;\n        try {\n            return eventHandler();\n        } finally{\n            currentPriorityLevel = previousPriorityLevel;\n        }\n    };\n    exports.unstable_scheduleCallback = function(priorityLevel, callback, options) {\n        var currentTime = exports.unstable_now();\n        \"object\" === typeof options && null !== options ? (options = options.delay, options = \"number\" === typeof options && 0 < options ? currentTime + options : currentTime) : options = currentTime;\n        switch(priorityLevel){\n            case 1:\n                var timeout = -1;\n                break;\n            case 2:\n                timeout = 250;\n                break;\n            case 5:\n                timeout = 1073741823;\n                break;\n            case 4:\n                timeout = 1e4;\n                break;\n            default:\n                timeout = 5e3;\n        }\n        timeout = options + timeout;\n        priorityLevel = {\n            id: taskIdCounter++,\n            callback: callback,\n            priorityLevel: priorityLevel,\n            startTime: options,\n            expirationTime: timeout,\n            sortIndex: -1\n        };\n        options > currentTime ? (priorityLevel.sortIndex = options, push(timerQueue, priorityLevel), null === peek(taskQueue) && priorityLevel === peek(timerQueue) && (isHostTimeoutScheduled ? (localClearTimeout(taskTimeoutID), taskTimeoutID = -1) : isHostTimeoutScheduled = !0, requestHostTimeout(handleTimeout, options - currentTime))) : (priorityLevel.sortIndex = timeout, push(taskQueue, priorityLevel), isHostCallbackScheduled || isPerformingWork || (isHostCallbackScheduled = !0, requestHostCallback()));\n        return priorityLevel;\n    };\n    exports.unstable_shouldYield = shouldYieldToHost;\n    exports.unstable_wrapCallback = function(callback) {\n        var parentPriorityLevel = currentPriorityLevel;\n        return function() {\n            var previousPriorityLevel = currentPriorityLevel;\n            currentPriorityLevel = parentPriorityLevel;\n            try {\n                return callback.apply(this, arguments);\n            } finally{\n                currentPriorityLevel = previousPriorityLevel;\n            }\n        };\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@react-three/fiber/node_modules/scheduler/index.js ***!
  \*************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ./cjs/scheduler.development.js */ \"(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXRocmVlL2ZpYmVyL25vZGVfbW9kdWxlcy9zY2hlZHVsZXIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFFYixJQUFJQSxLQUFxQyxFQUFFLEVBRTFDLE1BQU07SUFDTEMseUtBQTBEO0FBQzVEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFkbWluaXN0cmF0b3JcXERvY3VtZW50c1xcM1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQHJlYWN0LXRocmVlXFxmaWJlclxcbm9kZV9tb2R1bGVzXFxzY2hlZHVsZXJcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9zY2hlZHVsZXIucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9zY2hlZHVsZXIuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6WyJwcm9jZXNzIiwibW9kdWxlIiwiZXhwb3J0cyIsInJlcXVpcmUiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js\n");

/***/ })

};
;