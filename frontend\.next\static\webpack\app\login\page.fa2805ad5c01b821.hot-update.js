"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/components/auth/SupabaseSignIn.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/SupabaseSignIn.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseSignIn: () => (/* binding */ SupabaseSignIn)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _components_ui_sign_in_flow_1__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/sign-in-flow-1 */ \"(app-pages-browser)/./src/components/ui/sign-in-flow-1.tsx\");\n/* __next_internal_client_entry_do_not_use__ SupabaseSignIn auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst AnimatedNavLink = (param)=>{\n    let { href, children } = param;\n    const defaultTextColor = 'text-gray-300';\n    const hoverTextColor = 'text-white';\n    const textSizeClass = 'text-sm';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: href,\n        className: \"group relative inline-block overflow-hidden h-5 flex items-center \".concat(textSizeClass),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col transition-transform duration-400 ease-out transform group-hover:-translate-y-1/2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: defaultTextColor,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: hoverTextColor,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n_c = AnimatedNavLink;\nconst MiniNavbar = ()=>{\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [headerShapeClass, setHeaderShapeClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('rounded-full');\n    const shapeTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const toggleMenu = ()=>{\n        setIsOpen(!isOpen);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MiniNavbar.useEffect\": ()=>{\n            if (shapeTimeoutRef.current) {\n                clearTimeout(shapeTimeoutRef.current);\n            }\n            if (isOpen) {\n                setHeaderShapeClass('rounded-xl');\n            } else {\n                shapeTimeoutRef.current = setTimeout({\n                    \"MiniNavbar.useEffect\": ()=>{\n                        setHeaderShapeClass('rounded-full');\n                    }\n                }[\"MiniNavbar.useEffect\"], 300);\n            }\n            return ({\n                \"MiniNavbar.useEffect\": ()=>{\n                    if (shapeTimeoutRef.current) {\n                        clearTimeout(shapeTimeoutRef.current);\n                    }\n                }\n            })[\"MiniNavbar.useEffect\"];\n        }\n    }[\"MiniNavbar.useEffect\"], [\n        isOpen\n    ]);\n    const navLinksData = [\n        {\n            label: 'Planos',\n            href: '#1'\n        },\n        {\n            label: 'Ferramentas',\n            href: '#2'\n        },\n        {\n            label: 'Contato',\n            href: '#3'\n        }\n    ];\n    const loginButtonElement = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: \"px-4 py-2 sm:px-3 text-xs sm:text-sm border border-[#333] bg-[rgba(31,31,31,0.62)] text-gray-300 rounded-full hover:border-white/50 hover:text-white transition-colors duration-200 w-full sm:w-auto\",\n        children: \"Entrar\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, undefined);\n    const signupButtonElement = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative group w-full sm:w-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 -m-2 rounded-full hidden sm:block bg-green-100 opacity-40 filter blur-lg pointer-events-none transition-all duration-300 ease-out group-hover:opacity-60 group-hover:blur-xl group-hover:-m-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"relative z-10 px-4 py-2 sm:px-3 text-xs sm:text-sm font-semibold text-black bg-gradient-to-br from-green-100 to-green-400 rounded-full hover:from-green-200 hover:to-green-400 transition-all duration-200 w-full sm:w-auto\",\n                children: \"Inscreva-se\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-6 left-1/2 transform -translate-x-1/2 z-20 flex flex-col items-center pl-6 pr-6 py-3 backdrop-blur-sm \".concat(headerShapeClass, \" border border-[#333] bg-[#1f1f1f57] w-[calc(100%-2rem)] sm:w-auto transition-[border-radius] duration-0 ease-in-out\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between w-full gap-x-6 sm:gap-x-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/logo.svg\",\n                            alt: \"Logo\",\n                            width: 144,\n                            height: 20,\n                            className: \"w-36 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden sm:flex items-center space-x-4 sm:space-x-6 text-sm\",\n                        children: navLinksData.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedNavLink, {\n                                href: link.href,\n                                children: link.label\n                            }, link.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden sm:flex items-center gap-2 sm:gap-3\",\n                        children: [\n                            loginButtonElement,\n                            signupButtonElement\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"sm:hidden flex items-center justify-center w-8 h-8 text-gray-300 focus:outline-none\",\n                        onClick: toggleMenu,\n                        \"aria-label\": isOpen ? 'Close Menu' : 'Open Menu',\n                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: \"2\",\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: \"2\",\n                                d: \"M4 6h16M4 12h16M4 18h16\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:hidden flex flex-col items-center w-full transition-all ease-in-out duration-300 overflow-hidden \".concat(isOpen ? 'max-h-[1000px] opacity-100 pt-4' : 'max-h-0 opacity-0 pt-0 pointer-events-none'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col items-center space-y-4 text-base w-full\",\n                        children: navLinksData.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: link.href,\n                                className: \"text-gray-300 hover:text-white transition-colors w-full text-center\",\n                                children: link.label\n                            }, link.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center space-y-4 mt-4 w-full\",\n                        children: [\n                            loginButtonElement,\n                            signupButtonElement\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MiniNavbar, \"sPyg8sgr/4Xq+iK5kZEzi1f9W1Y=\");\n_c1 = MiniNavbar;\nconst SupabaseSignIn = (param)=>{\n    let { className } = param;\n    _s1();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('email');\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        '',\n        '',\n        '',\n        '',\n        '',\n        ''\n    ]);\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('signin');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const codeInputRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [initialCanvasVisible, setInitialCanvasVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [reverseCanvasVisible, setReverseCanvasVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { signInWithEmail, signUpWithEmail, signInWithGoogle, signInWithOTP, verifyOTP, isLoading, error, user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    // Redirecionar se já estiver logado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SupabaseSignIn.useEffect\": ()=>{\n            if (user) {\n                router.push('/dashboard');\n            }\n        }\n    }[\"SupabaseSignIn.useEffect\"], [\n        user,\n        router\n    ]);\n    const handleEmailSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email) return;\n        if (mode === 'signin') {\n            // Para login, vamos direto para senha\n            setStep('password');\n        } else {\n            // Para signup, enviamos OTP\n            const result = await signInWithOTP(email);\n            if (result.success) {\n                setStep('code');\n            }\n        }\n    };\n    const handlePasswordSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email || !password) return;\n        let result;\n        if (mode === 'signup') {\n            // Para signup, registra o usuário com email e senha\n            result = await signUpWithEmail(email, password);\n        } else {\n            // Para signin, faz login com email e senha\n            result = await signInWithEmail(email, password);\n        }\n        if (result.success) {\n            setReverseCanvasVisible(true);\n            setTimeout(()=>{\n                setInitialCanvasVisible(false);\n            }, 50);\n            setTimeout(()=>{\n                setStep('success');\n            }, 2000);\n        }\n    };\n    const handleGoogleSignIn = async ()=>{\n        await signInWithGoogle();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SupabaseSignIn.useEffect\": ()=>{\n            if (step === 'code') {\n                setTimeout({\n                    \"SupabaseSignIn.useEffect\": ()=>{\n                        var _codeInputRefs_current_;\n                        (_codeInputRefs_current_ = codeInputRefs.current[0]) === null || _codeInputRefs_current_ === void 0 ? void 0 : _codeInputRefs_current_.focus();\n                    }\n                }[\"SupabaseSignIn.useEffect\"], 500);\n            }\n        }\n    }[\"SupabaseSignIn.useEffect\"], [\n        step\n    ]);\n    const handleCodeChange = (index, value)=>{\n        if (value.length <= 1) {\n            const newCode = [\n                ...code\n            ];\n            newCode[index] = value;\n            setCode(newCode);\n            if (value && index < 5) {\n                var _codeInputRefs_current_;\n                (_codeInputRefs_current_ = codeInputRefs.current[index + 1]) === null || _codeInputRefs_current_ === void 0 ? void 0 : _codeInputRefs_current_.focus();\n            }\n            if (index === 5 && value) {\n                const isComplete = newCode.every((digit)=>digit.length === 1);\n                if (isComplete) {\n                    handleOTPVerification(newCode.join(''));\n                }\n            }\n        }\n    };\n    const handleOTPVerification = async (otpCode)=>{\n        const result = await verifyOTP(email, otpCode);\n        if (result.success) {\n            // Após verificar o OTP no signup, o usuário deve definir uma senha\n            if (mode === 'signup') {\n                setStep('password');\n            } else {\n                // Se for signin com OTP, vai direto para sucesso\n                setReverseCanvasVisible(true);\n                setTimeout(()=>{\n                    setInitialCanvasVisible(false);\n                }, 50);\n                setTimeout(()=>{\n                    setStep('success');\n                }, 2000);\n            }\n        }\n    };\n    const handleKeyDown = (index, e)=>{\n        if (e.key === 'Backspace' && !code[index] && index > 0) {\n            var _codeInputRefs_current_;\n            (_codeInputRefs_current_ = codeInputRefs.current[index - 1]) === null || _codeInputRefs_current_ === void 0 ? void 0 : _codeInputRefs_current_.focus();\n        }\n    };\n    const handleBackClick = ()=>{\n        if (step === 'password') {\n            if (mode === 'signup') {\n                // No signup, volta para o código OTP\n                setStep('code');\n            } else {\n                // No signin, volta para o email\n                setStep('email');\n            }\n            setPassword('');\n        } else if (step === 'code') {\n            setStep('email');\n            setCode([\n                '',\n                '',\n                '',\n                '',\n                '',\n                ''\n            ]);\n        }\n        setReverseCanvasVisible(false);\n        setInitialCanvasVisible(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)('flex w-[100%] flex-col min-h-screen bg-black relative', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-0\",\n                children: [\n                    initialCanvasVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sign_in_flow_1__WEBPACK_IMPORTED_MODULE_7__.CanvasRevealEffect, {\n                            animationSpeed: 3,\n                            containerClassName: \"bg-black\",\n                            colors: [\n                                [\n                                    8,\n                                    210,\n                                    133\n                                ],\n                                [\n                                    255,\n                                    255,\n                                    255\n                                ]\n                            ],\n                            dotSize: 6,\n                            reverse: false\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, undefined),\n                    reverseCanvasVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sign_in_flow_1__WEBPACK_IMPORTED_MODULE_7__.CanvasRevealEffect, {\n                            animationSpeed: 4,\n                            containerClassName: \"bg-black\",\n                            colors: [\n                                [\n                                    8,\n                                    210,\n                                    133\n                                ],\n                                [\n                                    255,\n                                    255,\n                                    255\n                                ]\n                            ],\n                            dotSize: 6,\n                            reverse: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(0,0,0,1)_0%,_transparent_100%)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 right-0 h-1/3 bg-gradient-to-b from-black to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex flex-col flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniNavbar, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 flex-col lg:flex-row\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col justify-center items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mt-[150px] max-w-sm\",\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm text-center\",\n                                        children: error.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: step === 'email' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -100\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -100\n                                            },\n                                            transition: {\n                                                duration: 0.4,\n                                                ease: 'easeOut'\n                                            },\n                                            className: \"space-y-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white\",\n                                                                children: mode === 'signin' ? 'Bem-vindo de volta' : 'Criar conta'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-[1.8rem] text-white/70 font-light\",\n                                                                children: mode === 'signin' ? 'Entre na sua conta' : 'Junte-se a nós hoje'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleGoogleSignIn,\n                                                            disabled: isLoading,\n                                                            className: \"backdrop-blur-[2px] w-full flex items-center justify-center gap-2 bg-white/5 hover:bg-white/10 text-white border border-white/10 rounded-full py-3 px-4 transition-colors disabled:opacity-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: \"G\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        mode === 'signin' ? 'Entrar' : 'Registrar',\n                                                                        \" com Google\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-px bg-white/10 flex-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white/40 text-sm\",\n                                                                    children: \"ou\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-px bg-white/10 flex-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                            onSubmit: handleEmailSubmit,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"email\",\n                                                                        placeholder: \"<EMAIL>\",\n                                                                        value: email,\n                                                                        onChange: (e)=>setEmail(e.target.value),\n                                                                        disabled: isLoading,\n                                                                        className: \"w-full backdrop-blur-[1px] text-white border-1 border-white/10 rounded-full py-3 px-4 focus:outline-none focus:border focus:border-white/30 text-center disabled:opacity-50\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"submit\",\n                                                                        disabled: isLoading,\n                                                                        className: \"absolute right-1.5 top-1.5 text-white w-9 h-9 flex items-center justify-center rounded-full bg-white/10 hover:bg-white/20 transition-colors group overflow-hidden disabled:opacity-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"relative w-full h-full block overflow-hidden\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"absolute inset-0 flex items-center justify-center transition-transform duration-300 group-hover:translate-x-full\",\n                                                                                    children: \"→\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                                    lineNumber: 397,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"absolute inset-0 flex items-center justify-center transition-transform duration-300 -translate-x-full group-hover:translate-x-0\",\n                                                                                    children: \"→\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                                    lineNumber: 400,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                            lineNumber: 396,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                        lineNumber: 391,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setMode(mode === 'signin' ? 'signup' : 'signin'),\n                                                        className: \"text-white/50 hover:text-white/70 transition-colors text-sm\",\n                                                        children: mode === 'signin' ? 'Não tem conta? Registre-se' : 'Já tem conta? Entre'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-white/40 pt-10\",\n                                                    children: [\n                                                        \"Ao continuar, voc\\xea concorda com os\",\n                                                        ' ',\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"#\",\n                                                            className: \"underline text-white/40 hover:text-white/60 transition-colors\",\n                                                            children: \"Termos de Servi\\xe7o\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        ' ',\n                                                        \"e\",\n                                                        ' ',\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"#\",\n                                                            className: \"underline text-white/40 hover:text-white/60 transition-colors\",\n                                                            children: \"Pol\\xedtica de Privacidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \".\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, \"email-step\", true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, undefined) : step === 'password' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 100\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: 100\n                                            },\n                                            transition: {\n                                                duration: 0.4,\n                                                ease: 'easeOut'\n                                            },\n                                            className: \"space-y-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white\",\n                                                            children: \"Digite sua senha\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[1.25rem] text-white/50 font-light\",\n                                                            children: [\n                                                                \"Para \",\n                                                                email\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handlePasswordSubmit,\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: showPassword ? 'text' : 'password',\n                                                                    placeholder: \"Sua senha\",\n                                                                    value: password,\n                                                                    onChange: (e)=>setPassword(e.target.value),\n                                                                    disabled: isLoading,\n                                                                    className: \"w-full backdrop-blur-[1px] text-white border-1 border-white/10 rounded-full py-3 px-4 focus:outline-none focus:border focus:border-white/30 text-center disabled:opacity-50\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                                    className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/70 text-sm\",\n                                                                    children: showPassword ? 'Ocultar' : 'Mostrar'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex w-full gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                                    type: \"button\",\n                                                                    onClick: handleBackClick,\n                                                                    className: \"rounded-full bg-white text-black font-medium px-8 py-3 hover:bg-white/90 transition-colors w-[30%]\",\n                                                                    whileHover: {\n                                                                        scale: 1.02\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.98\n                                                                    },\n                                                                    transition: {\n                                                                        duration: 0.2\n                                                                    },\n                                                                    children: \"Voltar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                                    type: \"submit\",\n                                                                    disabled: isLoading || !password,\n                                                                    className: \"flex-1 rounded-full font-medium py-3 border transition-all duration-300 \".concat(password && !isLoading ? 'bg-white text-black border-transparent hover:bg-white/90 cursor-pointer' : 'bg-[#111] text-white/50 border-white/10 cursor-not-allowed'),\n                                                                    children: isLoading ? 'Entrando...' : 'Entrar'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, \"password-step\", true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 19\n                                        }, undefined) : step === 'code' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 100\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: 100\n                                            },\n                                            transition: {\n                                                duration: 0.4,\n                                                ease: 'easeOut'\n                                            },\n                                            className: \"space-y-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white\",\n                                                            children: \"C\\xf3digo enviado\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[1.25rem] text-white/50 font-light\",\n                                                            children: \"Verifique seu email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative rounded-full py-4 px-5 border border-white/10 bg-transparent\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: code.map((digit, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    ref: (el)=>{\n                                                                                        codeInputRefs.current[i] = el;\n                                                                                    },\n                                                                                    type: \"text\",\n                                                                                    inputMode: \"numeric\",\n                                                                                    pattern: \"[0-9]*\",\n                                                                                    maxLength: 1,\n                                                                                    value: digit,\n                                                                                    onChange: (e)=>handleCodeChange(i, e.target.value),\n                                                                                    onKeyDown: (e)=>handleKeyDown(i, e),\n                                                                                    disabled: isLoading,\n                                                                                    className: \"w-8 text-center text-xl bg-transparent text-white border-none focus:outline-none focus:ring-0 appearance-none disabled:opacity-50\",\n                                                                                    style: {\n                                                                                        caretColor: 'transparent'\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                                    lineNumber: 516,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                !digit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"absolute top-0 left-0 w-full h-full flex items-center justify-center pointer-events-none\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xl text-white\",\n                                                                                        children: \"0\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                                        lineNumber: 533,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                                    lineNumber: 532,\n                                                                                    columnNumber: 35\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                            lineNumber: 515,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        i < 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white/20 text-xl\",\n                                                                            children: \"|\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 41\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, i, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 29\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex w-full gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                            onClick: handleBackClick,\n                                                            className: \"rounded-full bg-white text-black font-medium px-8 py-3 hover:bg-white/90 transition-colors w-[30%]\",\n                                                            whileHover: {\n                                                                scale: 1.02\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.98\n                                                            },\n                                                            transition: {\n                                                                duration: 0.2\n                                                            },\n                                                            children: \"Voltar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                            disabled: isLoading,\n                                                            className: \"flex-1 rounded-full font-medium py-3 border bg-[#111] text-white/50 border-white/10 cursor-not-allowed\",\n                                                            children: isLoading ? 'Verificando...' : 'Digite o código'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, \"code-step\", true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 50\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.4,\n                                                ease: 'easeOut',\n                                                delay: 0.3\n                                            },\n                                            className: \"space-y-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white\",\n                                                            children: \"Sucesso!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[1.25rem] text-white/50 font-light\",\n                                                            children: \"Redirecionando...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    initial: {\n                                                        scale: 0.8,\n                                                        opacity: 0\n                                                    },\n                                                    animate: {\n                                                        scale: 1,\n                                                        opacity: 1\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5,\n                                                        delay: 0.5\n                                                    },\n                                                    className: \"py-10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mx-auto w-16 h-16 rounded-full bg-gradient-to-br from-white to-white/70 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-8 w-8 text-black\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, \"success-step\", true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(SupabaseSignIn, \"wo3yX5ouXPJ69JVbN9wvN5UtKBo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c2 = SupabaseSignIn;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"AnimatedNavLink\");\n$RefreshReg$(_c1, \"MiniNavbar\");\n$RefreshReg$(_c2, \"SupabaseSignIn\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/SupabaseSignIn.tsx\n"));

/***/ })

});