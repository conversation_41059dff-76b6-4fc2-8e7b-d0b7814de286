"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-use-measure";
exports.ids = ["vendor-chunks/react-use-measure"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-use-measure/dist/index.js":
/*!******************************************************!*\
  !*** ./node_modules/react-use-measure/dist/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ j)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nfunction g(n,t){let o;return(...i)=>{window.clearTimeout(o),o=window.setTimeout(()=>n(...i),t)}}function j({debounce:n,scroll:t,polyfill:o,offsetSize:i}={debounce:0,scroll:!1,offsetSize:!1}){const a=o||(typeof window==\"undefined\"?class{}:window.ResizeObserver);if(!a)throw new Error(\"This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills\");const[c,h]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({left:0,top:0,width:0,height:0,bottom:0,right:0,x:0,y:0}),e=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({element:null,scrollContainers:null,resizeObserver:null,lastBounds:c,orientationHandler:null}),d=n?typeof n==\"number\"?n:n.scroll:null,f=n?typeof n==\"number\"?n:n.resize:null,w=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(!1);(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>(w.current=!0,()=>void(w.current=!1)));const[z,m,s]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{const r=()=>{if(!e.current.element)return;const{left:y,top:C,width:H,height:O,bottom:S,right:x,x:B,y:R}=e.current.element.getBoundingClientRect(),l={left:y,top:C,width:H,height:O,bottom:S,right:x,x:B,y:R};e.current.element instanceof HTMLElement&&i&&(l.height=e.current.element.offsetHeight,l.width=e.current.element.offsetWidth),Object.freeze(l),w.current&&!D(e.current.lastBounds,l)&&h(e.current.lastBounds=l)};return[r,f?g(r,f):r,d?g(r,d):r]},[h,i,d,f]);function v(){e.current.scrollContainers&&(e.current.scrollContainers.forEach(r=>r.removeEventListener(\"scroll\",s,!0)),e.current.scrollContainers=null),e.current.resizeObserver&&(e.current.resizeObserver.disconnect(),e.current.resizeObserver=null),e.current.orientationHandler&&(\"orientation\"in screen&&\"removeEventListener\"in screen.orientation?screen.orientation.removeEventListener(\"change\",e.current.orientationHandler):\"onorientationchange\"in window&&window.removeEventListener(\"orientationchange\",e.current.orientationHandler))}function b(){e.current.element&&(e.current.resizeObserver=new a(s),e.current.resizeObserver.observe(e.current.element),t&&e.current.scrollContainers&&e.current.scrollContainers.forEach(r=>r.addEventListener(\"scroll\",s,{capture:!0,passive:!0})),e.current.orientationHandler=()=>{s()},\"orientation\"in screen&&\"addEventListener\"in screen.orientation?screen.orientation.addEventListener(\"change\",e.current.orientationHandler):\"onorientationchange\"in window&&window.addEventListener(\"orientationchange\",e.current.orientationHandler))}const L=r=>{!r||r===e.current.element||(v(),e.current.element=r,e.current.scrollContainers=E(r),b())};return X(s,!!t),W(m),(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{v(),b()},[t,s,m]),(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>v,[]),[L,c,z]}function W(n){(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{const t=n;return window.addEventListener(\"resize\",t),()=>void window.removeEventListener(\"resize\",t)},[n])}function X(n,t){(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{if(t){const o=n;return window.addEventListener(\"scroll\",o,{capture:!0,passive:!0}),()=>void window.removeEventListener(\"scroll\",o,!0)}},[n,t])}function E(n){const t=[];if(!n||n===document.body)return t;const{overflow:o,overflowX:i,overflowY:a}=window.getComputedStyle(n);return[o,i,a].some(c=>c===\"auto\"||c===\"scroll\")&&t.push(n),[...t,...E(n.parentElement)]}const k=[\"x\",\"y\",\"top\",\"bottom\",\"left\",\"right\",\"width\",\"height\"],D=(n,t)=>k.every(o=>n[o]===t[o]);\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-use-measure/dist/index.js\n");

/***/ })

};
;