"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/components/auth/SupabaseSignIn.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/SupabaseSignIn.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseSignIn: () => (/* binding */ SupabaseSignIn)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _components_ui_sign_in_flow_1__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/sign-in-flow-1 */ \"(app-pages-browser)/./src/components/ui/sign-in-flow-1.tsx\");\n/* __next_internal_client_entry_do_not_use__ SupabaseSignIn auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst AnimatedNavLink = (param)=>{\n    let { href, children } = param;\n    const defaultTextColor = 'text-gray-300';\n    const hoverTextColor = 'text-white';\n    const textSizeClass = 'text-sm';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: href,\n        className: \"group relative inline-block overflow-hidden h-5 flex items-center \".concat(textSizeClass),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col transition-transform duration-400 ease-out transform group-hover:-translate-y-1/2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: defaultTextColor,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: hoverTextColor,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n_c = AnimatedNavLink;\nconst MiniNavbar = ()=>{\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [headerShapeClass, setHeaderShapeClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('rounded-full');\n    const shapeTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const toggleMenu = ()=>{\n        setIsOpen(!isOpen);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MiniNavbar.useEffect\": ()=>{\n            if (shapeTimeoutRef.current) {\n                clearTimeout(shapeTimeoutRef.current);\n            }\n            if (isOpen) {\n                setHeaderShapeClass('rounded-xl');\n            } else {\n                shapeTimeoutRef.current = setTimeout({\n                    \"MiniNavbar.useEffect\": ()=>{\n                        setHeaderShapeClass('rounded-full');\n                    }\n                }[\"MiniNavbar.useEffect\"], 300);\n            }\n            return ({\n                \"MiniNavbar.useEffect\": ()=>{\n                    if (shapeTimeoutRef.current) {\n                        clearTimeout(shapeTimeoutRef.current);\n                    }\n                }\n            })[\"MiniNavbar.useEffect\"];\n        }\n    }[\"MiniNavbar.useEffect\"], [\n        isOpen\n    ]);\n    const navLinksData = [\n        {\n            label: 'Planos',\n            href: '#1'\n        },\n        {\n            label: 'Ferramentas',\n            href: '#2'\n        },\n        {\n            label: 'Contato',\n            href: '#3'\n        }\n    ];\n    const loginButtonElement = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: \"px-4 py-2 sm:px-3 text-xs sm:text-sm border border-[#333] bg-[rgba(31,31,31,0.62)] text-gray-300 rounded-full hover:border-white/50 hover:text-white transition-colors duration-200 w-full sm:w-auto\",\n        children: \"Entrar\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, undefined);\n    const signupButtonElement = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative group w-full sm:w-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 -m-2 rounded-full hidden sm:block bg-green-100 opacity-40 filter blur-lg pointer-events-none transition-all duration-300 ease-out group-hover:opacity-60 group-hover:blur-xl group-hover:-m-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"relative z-10 px-4 py-2 sm:px-3 text-xs sm:text-sm font-semibold text-black bg-gradient-to-br from-green-100 to-green-400 rounded-full hover:from-green-200 hover:to-green-400 transition-all duration-200 w-full sm:w-auto\",\n                children: \"Inscreva-se\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-6 left-1/2 transform -translate-x-1/2 z-20 flex flex-col items-center pl-6 pr-6 py-3 backdrop-blur-sm \".concat(headerShapeClass, \" border border-[#333] bg-[#1f1f1f57] w-[calc(100%-2rem)] sm:w-auto transition-[border-radius] duration-0 ease-in-out\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between w-full gap-x-6 sm:gap-x-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/logo.svg\",\n                            alt: \"Logo\",\n                            width: 144,\n                            height: 20,\n                            className: \"w-36 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden sm:flex items-center space-x-4 sm:space-x-6 text-sm\",\n                        children: navLinksData.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedNavLink, {\n                                href: link.href,\n                                children: link.label\n                            }, link.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden sm:flex items-center gap-2 sm:gap-3\",\n                        children: [\n                            loginButtonElement,\n                            signupButtonElement\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"sm:hidden flex items-center justify-center w-8 h-8 text-gray-300 focus:outline-none\",\n                        onClick: toggleMenu,\n                        \"aria-label\": isOpen ? 'Close Menu' : 'Open Menu',\n                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: \"2\",\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: \"2\",\n                                d: \"M4 6h16M4 12h16M4 18h16\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:hidden flex flex-col items-center w-full transition-all ease-in-out duration-300 overflow-hidden \".concat(isOpen ? 'max-h-[1000px] opacity-100 pt-4' : 'max-h-0 opacity-0 pt-0 pointer-events-none'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col items-center space-y-4 text-base w-full\",\n                        children: navLinksData.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: link.href,\n                                className: \"text-gray-300 hover:text-white transition-colors w-full text-center\",\n                                children: link.label\n                            }, link.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center space-y-4 mt-4 w-full\",\n                        children: [\n                            loginButtonElement,\n                            signupButtonElement\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MiniNavbar, \"sPyg8sgr/4Xq+iK5kZEzi1f9W1Y=\");\n_c1 = MiniNavbar;\nconst SupabaseSignIn = (param)=>{\n    let { className } = param;\n    _s1();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('email');\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        '',\n        '',\n        '',\n        '',\n        '',\n        ''\n    ]);\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('signin');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const codeInputRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [initialCanvasVisible, setInitialCanvasVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [reverseCanvasVisible, setReverseCanvasVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { signInWithEmail, signUpWithEmail, signInWithGoogle, signInWithOTP, verifyOTP, isLoading, error, user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    // Redirecionar se já estiver logado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SupabaseSignIn.useEffect\": ()=>{\n            if (user) {\n                router.push('/dashboard');\n            }\n        }\n    }[\"SupabaseSignIn.useEffect\"], [\n        user,\n        router\n    ]);\n    const handleEmailSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email) return;\n        if (mode === 'signin') {\n            // Para login, vamos direto para senha\n            setStep('password');\n        } else {\n            // Para signup, enviamos OTP\n            const result = await signInWithOTP(email);\n            if (result.success) {\n                setStep('code');\n            }\n        }\n    };\n    const handlePasswordSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email || !password) return;\n        let result;\n        if (mode === 'signup') {\n            // Para signup, registra o usuário com email e senha\n            result = await signUpWithEmail(email, password);\n        } else {\n            // Para signin, faz login com email e senha\n            result = await signInWithEmail(email, password);\n        }\n        if (result.success) {\n            setReverseCanvasVisible(true);\n            setTimeout(()=>{\n                setInitialCanvasVisible(false);\n            }, 50);\n            setTimeout(()=>{\n                setStep('success');\n            }, 2000);\n        }\n    };\n    const handleGoogleSignIn = async ()=>{\n        await signInWithGoogle();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SupabaseSignIn.useEffect\": ()=>{\n            if (step === 'code') {\n                setTimeout({\n                    \"SupabaseSignIn.useEffect\": ()=>{\n                        var _codeInputRefs_current_;\n                        (_codeInputRefs_current_ = codeInputRefs.current[0]) === null || _codeInputRefs_current_ === void 0 ? void 0 : _codeInputRefs_current_.focus();\n                    }\n                }[\"SupabaseSignIn.useEffect\"], 500);\n            }\n        }\n    }[\"SupabaseSignIn.useEffect\"], [\n        step\n    ]);\n    const handleCodeChange = (index, value)=>{\n        if (value.length <= 1) {\n            const newCode = [\n                ...code\n            ];\n            newCode[index] = value;\n            setCode(newCode);\n            if (value && index < 5) {\n                var _codeInputRefs_current_;\n                (_codeInputRefs_current_ = codeInputRefs.current[index + 1]) === null || _codeInputRefs_current_ === void 0 ? void 0 : _codeInputRefs_current_.focus();\n            }\n            if (index === 5 && value) {\n                const isComplete = newCode.every((digit)=>digit.length === 1);\n                if (isComplete) {\n                    handleOTPVerification(newCode.join(''));\n                }\n            }\n        }\n    };\n    const handleOTPVerification = async (otpCode)=>{\n        const result = await verifyOTP(email, otpCode);\n        if (result.success) {\n            // Após verificar o OTP no signup, o usuário deve definir uma senha\n            if (mode === 'signup') {\n                setStep('password');\n            } else {\n                // Se for signin com OTP, vai direto para sucesso\n                setReverseCanvasVisible(true);\n                setTimeout(()=>{\n                    setInitialCanvasVisible(false);\n                }, 50);\n                setTimeout(()=>{\n                    setStep('success');\n                }, 2000);\n            }\n        }\n    };\n    const handleKeyDown = (index, e)=>{\n        if (e.key === 'Backspace' && !code[index] && index > 0) {\n            var _codeInputRefs_current_;\n            (_codeInputRefs_current_ = codeInputRefs.current[index - 1]) === null || _codeInputRefs_current_ === void 0 ? void 0 : _codeInputRefs_current_.focus();\n        }\n    };\n    const handleBackClick = ()=>{\n        if (step === 'password') {\n            // Volta para o email\n            setStep('email');\n            setPassword('');\n        } else if (step === 'code') {\n            setStep('email');\n            setCode([\n                '',\n                '',\n                '',\n                '',\n                '',\n                ''\n            ]);\n        }\n        setReverseCanvasVisible(false);\n        setInitialCanvasVisible(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)('flex w-[100%] flex-col min-h-screen bg-black relative', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-0\",\n                children: [\n                    initialCanvasVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sign_in_flow_1__WEBPACK_IMPORTED_MODULE_7__.CanvasRevealEffect, {\n                            animationSpeed: 3,\n                            containerClassName: \"bg-black\",\n                            colors: [\n                                [\n                                    8,\n                                    210,\n                                    133\n                                ],\n                                [\n                                    255,\n                                    255,\n                                    255\n                                ]\n                            ],\n                            dotSize: 6,\n                            reverse: false\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, undefined),\n                    reverseCanvasVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sign_in_flow_1__WEBPACK_IMPORTED_MODULE_7__.CanvasRevealEffect, {\n                            animationSpeed: 4,\n                            containerClassName: \"bg-black\",\n                            colors: [\n                                [\n                                    8,\n                                    210,\n                                    133\n                                ],\n                                [\n                                    255,\n                                    255,\n                                    255\n                                ]\n                            ],\n                            dotSize: 6,\n                            reverse: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(0,0,0,1)_0%,_transparent_100%)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 right-0 h-1/3 bg-gradient-to-b from-black to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex flex-col flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniNavbar, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 flex-col lg:flex-row\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col justify-center items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mt-[150px] max-w-sm\",\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm text-center\",\n                                        children: error.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: step === 'email' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -100\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -100\n                                            },\n                                            transition: {\n                                                duration: 0.4,\n                                                ease: 'easeOut'\n                                            },\n                                            className: \"space-y-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white\",\n                                                                children: mode === 'signin' ? 'Bem-vindo de volta' : 'Acesse sua conta'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-[1.8rem] text-white/70 font-light\",\n                                                                children: mode === 'signin' ? 'Entre com sua senha' : 'Entre ou registre-se com código'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleGoogleSignIn,\n                                                            disabled: isLoading,\n                                                            className: \"backdrop-blur-[2px] w-full flex items-center justify-center gap-2 bg-white/5 hover:bg-white/10 text-white border border-white/10 rounded-full py-3 px-4 transition-colors disabled:opacity-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: \"G\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        mode === 'signin' ? 'Entrar' : 'Registrar',\n                                                                        \" com Google\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-px bg-white/10 flex-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white/40 text-sm\",\n                                                                    children: \"ou\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-px bg-white/10 flex-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                            onSubmit: handleEmailSubmit,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"email\",\n                                                                        placeholder: \"<EMAIL>\",\n                                                                        value: email,\n                                                                        onChange: (e)=>setEmail(e.target.value),\n                                                                        disabled: isLoading,\n                                                                        className: \"w-full backdrop-blur-[1px] text-white border-1 border-white/10 rounded-full py-3 px-4 focus:outline-none focus:border focus:border-white/30 text-center disabled:opacity-50\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"submit\",\n                                                                        disabled: isLoading,\n                                                                        className: \"absolute right-1.5 top-1.5 text-white w-9 h-9 flex items-center justify-center rounded-full bg-white/10 hover:bg-white/20 transition-colors group overflow-hidden disabled:opacity-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"relative w-full h-full block overflow-hidden\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"absolute inset-0 flex items-center justify-center transition-transform duration-300 group-hover:translate-x-full\",\n                                                                                    children: \"→\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                                    lineNumber: 392,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"absolute inset-0 flex items-center justify-center transition-transform duration-300 -translate-x-full group-hover:translate-x-0\",\n                                                                                    children: \"→\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                                    lineNumber: 395,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                            lineNumber: 391,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                        lineNumber: 386,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-px bg-white/10 flex-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white/40 text-sm\",\n                                                                    children: \"M\\xe9todo de acesso\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-px bg-white/10 flex-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setMode('signin'),\n                                                                    className: \"px-4 py-2 rounded-full text-sm transition-colors \".concat(mode === 'signin' ? 'bg-white text-black' : 'bg-white/10 text-white/70 hover:bg-white/20'),\n                                                                    children: \"Com senha\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setMode('otp'),\n                                                                    className: \"px-4 py-2 rounded-full text-sm transition-colors \".concat(mode === 'otp' ? 'bg-white text-black' : 'bg-white/10 text-white/70 hover:bg-white/20'),\n                                                                    children: \"Com c\\xf3digo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-white/40 pt-10\",\n                                                    children: [\n                                                        \"Ao continuar, voc\\xea concorda com os\",\n                                                        ' ',\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"#\",\n                                                            className: \"underline text-white/40 hover:text-white/60 transition-colors\",\n                                                            children: \"Termos de Servi\\xe7o\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        ' ',\n                                                        \"e\",\n                                                        ' ',\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"#\",\n                                                            className: \"underline text-white/40 hover:text-white/60 transition-colors\",\n                                                            children: \"Pol\\xedtica de Privacidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \".\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, \"email-step\", true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 19\n                                        }, undefined) : step === 'password' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 100\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: 100\n                                            },\n                                            transition: {\n                                                duration: 0.4,\n                                                ease: 'easeOut'\n                                            },\n                                            className: \"space-y-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white\",\n                                                            children: \"Digite sua senha\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[1.25rem] text-white/50 font-light\",\n                                                            children: [\n                                                                \"Para \",\n                                                                email\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handlePasswordSubmit,\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: showPassword ? 'text' : 'password',\n                                                                    placeholder: \"Sua senha\",\n                                                                    value: password,\n                                                                    onChange: (e)=>setPassword(e.target.value),\n                                                                    disabled: isLoading,\n                                                                    className: \"w-full backdrop-blur-[1px] text-white border-1 border-white/10 rounded-full py-3 px-4 focus:outline-none focus:border focus:border-white/30 text-center disabled:opacity-50\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                                    className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/70 text-sm\",\n                                                                    children: showPassword ? 'Ocultar' : 'Mostrar'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex w-full gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                                    type: \"button\",\n                                                                    onClick: handleBackClick,\n                                                                    className: \"rounded-full bg-white text-black font-medium px-8 py-3 hover:bg-white/90 transition-colors w-[30%]\",\n                                                                    whileHover: {\n                                                                        scale: 1.02\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.98\n                                                                    },\n                                                                    transition: {\n                                                                        duration: 0.2\n                                                                    },\n                                                                    children: \"Voltar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                                    type: \"submit\",\n                                                                    disabled: isLoading || !password,\n                                                                    className: \"flex-1 rounded-full font-medium py-3 border transition-all duration-300 \".concat(password && !isLoading ? 'bg-white text-black border-transparent hover:bg-white/90 cursor-pointer' : 'bg-[#111] text-white/50 border-white/10 cursor-not-allowed'),\n                                                                    children: isLoading ? 'Entrando...' : 'Entrar'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, \"password-step\", true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 19\n                                        }, undefined) : step === 'code' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 100\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: 100\n                                            },\n                                            transition: {\n                                                duration: 0.4,\n                                                ease: 'easeOut'\n                                            },\n                                            className: \"space-y-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white\",\n                                                            children: \"C\\xf3digo enviado\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[1.25rem] text-white/50 font-light\",\n                                                            children: \"Verifique seu email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative rounded-full py-4 px-5 border border-white/10 bg-transparent\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: code.map((digit, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    ref: (el)=>{\n                                                                                        codeInputRefs.current[i] = el;\n                                                                                    },\n                                                                                    type: \"text\",\n                                                                                    inputMode: \"numeric\",\n                                                                                    pattern: \"[0-9]*\",\n                                                                                    maxLength: 1,\n                                                                                    value: digit,\n                                                                                    onChange: (e)=>handleCodeChange(i, e.target.value),\n                                                                                    onKeyDown: (e)=>handleKeyDown(i, e),\n                                                                                    disabled: isLoading,\n                                                                                    className: \"w-8 text-center text-xl bg-transparent text-white border-none focus:outline-none focus:ring-0 appearance-none disabled:opacity-50\",\n                                                                                    style: {\n                                                                                        caretColor: 'transparent'\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                                    lineNumber: 533,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                !digit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"absolute top-0 left-0 w-full h-full flex items-center justify-center pointer-events-none\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xl text-white\",\n                                                                                        children: \"0\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                                        lineNumber: 550,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                                    lineNumber: 549,\n                                                                                    columnNumber: 35\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        i < 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white/20 text-xl\",\n                                                                            children: \"|\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                            lineNumber: 554,\n                                                                            columnNumber: 41\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, i, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 29\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex w-full gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                            onClick: handleBackClick,\n                                                            className: \"rounded-full bg-white text-black font-medium px-8 py-3 hover:bg-white/90 transition-colors w-[30%]\",\n                                                            whileHover: {\n                                                                scale: 1.02\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.98\n                                                            },\n                                                            transition: {\n                                                                duration: 0.2\n                                                            },\n                                                            children: \"Voltar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                            disabled: isLoading,\n                                                            className: \"flex-1 rounded-full font-medium py-3 border bg-[#111] text-white/50 border-white/10 cursor-not-allowed\",\n                                                            children: isLoading ? 'Verificando...' : 'Digite o código'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, \"code-step\", true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 50\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.4,\n                                                ease: 'easeOut',\n                                                delay: 0.3\n                                            },\n                                            className: \"space-y-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white\",\n                                                            children: \"Sucesso!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[1.25rem] text-white/50 font-light\",\n                                                            children: \"Redirecionando...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    initial: {\n                                                        scale: 0.8,\n                                                        opacity: 0\n                                                    },\n                                                    animate: {\n                                                        scale: 1,\n                                                        opacity: 1\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5,\n                                                        delay: 0.5\n                                                    },\n                                                    className: \"py-10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mx-auto w-16 h-16 rounded-full bg-gradient-to-br from-white to-white/70 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-8 w-8 text-black\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                lineNumber: 609,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, \"success-step\", true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 318,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(SupabaseSignIn, \"wo3yX5ouXPJ69JVbN9wvN5UtKBo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c2 = SupabaseSignIn;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"AnimatedNavLink\");\n$RefreshReg$(_c1, \"MiniNavbar\");\n$RefreshReg$(_c2, \"SupabaseSignIn\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/SupabaseSignIn.tsx\n"));

/***/ })

});