"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \n\nconst useAuth = ()=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            // Obter sessão inicial\n            const getInitialSession = {\n                \"useAuth.useEffect.getInitialSession\": async ()=>{\n                    const { data: { session }, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.getSession();\n                    if (error) {\n                        setError({\n                            message: error.message\n                        });\n                    } else {\n                        setSession(session);\n                        var _session_user;\n                        setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                    }\n                    setIsLoading(false);\n                }\n            }[\"useAuth.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Escutar mudanças de autenticação\n            const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.onAuthStateChange({\n                \"useAuth.useEffect\": async (event, session)=>{\n                    setSession(session);\n                    var _session_user;\n                    setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                    setIsLoading(false);\n                    if (event === 'SIGNED_OUT') {\n                        setError(null);\n                    }\n                }\n            }[\"useAuth.useEffect\"]);\n            return ({\n                \"useAuth.useEffect\": ()=>subscription.unsubscribe()\n            })[\"useAuth.useEffect\"];\n        }\n    }[\"useAuth.useEffect\"], []);\n    // Função para login com email/senha\n    const signInWithEmail = async (email, password)=>{\n        setIsLoading(true);\n        setError(null);\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            setError({\n                message: error.message\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error\n            };\n        }\n        setIsLoading(false);\n        return {\n            success: true,\n            data\n        };\n    };\n    // Função para registro com email/senha (direto)\n    const signUpWithEmail = async (email, password)=>{\n        setIsLoading(true);\n        setError(null);\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signUp({\n            email,\n            password\n        });\n        if (error) {\n            setError({\n                message: error.message\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error\n            };\n        }\n        setIsLoading(false);\n        return {\n            success: true,\n            data\n        };\n    };\n    // Função para finalizar signup após OTP (definir senha final)\n    const completeSignupWithPassword = async (email, password)=>{\n        setIsLoading(true);\n        setError(null);\n        // Atualizar a senha do usuário\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.updateUser({\n            password: password\n        });\n        if (error) {\n            // Se falhar, tentar fazer signup novamente com a senha correta\n            const { data: signUpData, error: signUpError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signUp({\n                email,\n                password\n            });\n            if (signUpError) {\n                setError({\n                    message: signUpError.message\n                });\n                setIsLoading(false);\n                return {\n                    success: false,\n                    error: signUpError\n                };\n            }\n            setIsLoading(false);\n            return {\n                success: true,\n                data: signUpData\n            };\n        }\n        setIsLoading(false);\n        return {\n            success: true,\n            data\n        };\n    };\n    // Função para login com Google\n    const signInWithGoogle = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signInWithOAuth({\n            provider: 'google',\n            options: {\n                redirectTo: \"\".concat(window.location.origin, \"/dashboard\")\n            }\n        });\n        if (error) {\n            setError({\n                message: error.message\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error\n            };\n        }\n        return {\n            success: true,\n            data\n        };\n    };\n    // Função para enviar OTP por email (para signup)\n    const signUpWithOTP = async (email)=>{\n        setIsLoading(true);\n        setError(null);\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signInWithOtp({\n            email,\n            options: {\n                shouldCreateUser: true\n            }\n        });\n        if (error) {\n            setError({\n                message: error.message\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error\n            };\n        }\n        setIsLoading(false);\n        return {\n            success: true,\n            data\n        };\n    };\n    // Função para verificar OTP (para signup)\n    const verifySignupOTP = async (email, token)=>{\n        setIsLoading(true);\n        setError(null);\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.verifyOtp({\n            email,\n            token,\n            type: 'signup'\n        });\n        if (error) {\n            setError({\n                message: error.message\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error\n            };\n        }\n        // Fazer logout imediatamente para não autenticar o usuário ainda\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signOut();\n        setIsLoading(false);\n        return {\n            success: true,\n            data\n        };\n    };\n    // Função para logout\n    const signOut = async ()=>{\n        setIsLoading(true);\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signOut();\n        if (error) {\n            setError({\n                message: error.message\n            });\n        }\n        setIsLoading(false);\n        return {\n            success: !error,\n            error\n        };\n    };\n    return {\n        user,\n        session,\n        isLoading,\n        error,\n        signInWithEmail,\n        signUpWithEmail,\n        completeSignupWithPassword,\n        signInWithGoogle,\n        signUpWithOTP,\n        verifySignupOTP,\n        signOut\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAuth.ts\n"));

/***/ })

});