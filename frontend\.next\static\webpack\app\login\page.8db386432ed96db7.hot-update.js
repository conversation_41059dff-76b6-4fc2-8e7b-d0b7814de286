"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/components/auth/SupabaseSignIn.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/SupabaseSignIn.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseSignIn: () => (/* binding */ SupabaseSignIn)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _components_ui_sign_in_flow_1__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/sign-in-flow-1 */ \"(app-pages-browser)/./src/components/ui/sign-in-flow-1.tsx\");\n/* __next_internal_client_entry_do_not_use__ SupabaseSignIn auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst AnimatedNavLink = (param)=>{\n    let { href, children } = param;\n    const defaultTextColor = 'text-gray-300';\n    const hoverTextColor = 'text-white';\n    const textSizeClass = 'text-sm';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: href,\n        className: \"group relative inline-block overflow-hidden h-5 flex items-center \".concat(textSizeClass),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col transition-transform duration-400 ease-out transform group-hover:-translate-y-1/2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: defaultTextColor,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: hoverTextColor,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n_c = AnimatedNavLink;\nconst MiniNavbar = ()=>{\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [headerShapeClass, setHeaderShapeClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('rounded-full');\n    const shapeTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const toggleMenu = ()=>{\n        setIsOpen(!isOpen);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MiniNavbar.useEffect\": ()=>{\n            if (shapeTimeoutRef.current) {\n                clearTimeout(shapeTimeoutRef.current);\n            }\n            if (isOpen) {\n                setHeaderShapeClass('rounded-xl');\n            } else {\n                shapeTimeoutRef.current = setTimeout({\n                    \"MiniNavbar.useEffect\": ()=>{\n                        setHeaderShapeClass('rounded-full');\n                    }\n                }[\"MiniNavbar.useEffect\"], 300);\n            }\n            return ({\n                \"MiniNavbar.useEffect\": ()=>{\n                    if (shapeTimeoutRef.current) {\n                        clearTimeout(shapeTimeoutRef.current);\n                    }\n                }\n            })[\"MiniNavbar.useEffect\"];\n        }\n    }[\"MiniNavbar.useEffect\"], [\n        isOpen\n    ]);\n    const navLinksData = [\n        {\n            label: 'Planos',\n            href: '#1'\n        },\n        {\n            label: 'Ferramentas',\n            href: '#2'\n        },\n        {\n            label: 'Contato',\n            href: '#3'\n        }\n    ];\n    const loginButtonElement = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: \"px-4 py-2 sm:px-3 text-xs sm:text-sm border border-[#333] bg-[rgba(31,31,31,0.62)] text-gray-300 rounded-full hover:border-white/50 hover:text-white transition-colors duration-200 w-full sm:w-auto\",\n        children: \"Entrar\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, undefined);\n    const signupButtonElement = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative group w-full sm:w-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 -m-2 rounded-full hidden sm:block bg-green-100 opacity-40 filter blur-lg pointer-events-none transition-all duration-300 ease-out group-hover:opacity-60 group-hover:blur-xl group-hover:-m-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"relative z-10 px-4 py-2 sm:px-3 text-xs sm:text-sm font-semibold text-black bg-gradient-to-br from-green-100 to-green-400 rounded-full hover:from-green-200 hover:to-green-400 transition-all duration-200 w-full sm:w-auto\",\n                children: \"Inscreva-se\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-6 left-1/2 transform -translate-x-1/2 z-20 flex flex-col items-center pl-6 pr-6 py-3 backdrop-blur-sm \".concat(headerShapeClass, \" border border-[#333] bg-[#1f1f1f57] w-[calc(100%-2rem)] sm:w-auto transition-[border-radius] duration-0 ease-in-out\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between w-full gap-x-6 sm:gap-x-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/logo.svg\",\n                            alt: \"Logo\",\n                            width: 144,\n                            height: 20,\n                            className: \"w-36 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden sm:flex items-center space-x-4 sm:space-x-6 text-sm\",\n                        children: navLinksData.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedNavLink, {\n                                href: link.href,\n                                children: link.label\n                            }, link.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden sm:flex items-center gap-2 sm:gap-3\",\n                        children: [\n                            loginButtonElement,\n                            signupButtonElement\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"sm:hidden flex items-center justify-center w-8 h-8 text-gray-300 focus:outline-none\",\n                        onClick: toggleMenu,\n                        \"aria-label\": isOpen ? 'Close Menu' : 'Open Menu',\n                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: \"2\",\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: \"2\",\n                                d: \"M4 6h16M4 12h16M4 18h16\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:hidden flex flex-col items-center w-full transition-all ease-in-out duration-300 overflow-hidden \".concat(isOpen ? 'max-h-[1000px] opacity-100 pt-4' : 'max-h-0 opacity-0 pt-0 pointer-events-none'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col items-center space-y-4 text-base w-full\",\n                        children: navLinksData.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: link.href,\n                                className: \"text-gray-300 hover:text-white transition-colors w-full text-center\",\n                                children: link.label\n                            }, link.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center space-y-4 mt-4 w-full\",\n                        children: [\n                            loginButtonElement,\n                            signupButtonElement\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MiniNavbar, \"sPyg8sgr/4Xq+iK5kZEzi1f9W1Y=\");\n_c1 = MiniNavbar;\nconst SupabaseSignIn = (param)=>{\n    let { className } = param;\n    _s1();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('email');\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        '',\n        '',\n        '',\n        '',\n        '',\n        ''\n    ]);\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('signin');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const codeInputRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [initialCanvasVisible, setInitialCanvasVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [reverseCanvasVisible, setReverseCanvasVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { signInWithEmail, signUpWithEmail, completeSignupWithPassword, signInWithGoogle, signInWithOTP, verifyOTP, isLoading, error, user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    // Redirecionar se já estiver logado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SupabaseSignIn.useEffect\": ()=>{\n            if (user) {\n                router.push('/dashboard');\n            }\n        }\n    }[\"SupabaseSignIn.useEffect\"], [\n        user,\n        router\n    ]);\n    const handleEmailSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email) return;\n        if (mode === 'signin') {\n            // Para login, vamos direto para senha\n            setStep('password');\n        } else {\n            // Para signup, enviamos OTP\n            const result = await signInWithOTP(email);\n            if (result.success) {\n                setStep('code');\n            }\n        }\n    };\n    const handlePasswordSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email || !password) return;\n        let result;\n        if (mode === 'signup') {\n            // Para signup, registra o usuário com email e senha\n            result = await signUpWithEmail(email, password);\n        } else {\n            // Para signin, faz login com email e senha\n            result = await signInWithEmail(email, password);\n        }\n        if (result.success) {\n            setReverseCanvasVisible(true);\n            setTimeout(()=>{\n                setInitialCanvasVisible(false);\n            }, 50);\n            setTimeout(()=>{\n                setStep('success');\n            }, 2000);\n        }\n    };\n    const handleGoogleSignIn = async ()=>{\n        await signInWithGoogle();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SupabaseSignIn.useEffect\": ()=>{\n            if (step === 'code') {\n                setTimeout({\n                    \"SupabaseSignIn.useEffect\": ()=>{\n                        var _codeInputRefs_current_;\n                        (_codeInputRefs_current_ = codeInputRefs.current[0]) === null || _codeInputRefs_current_ === void 0 ? void 0 : _codeInputRefs_current_.focus();\n                    }\n                }[\"SupabaseSignIn.useEffect\"], 500);\n            }\n        }\n    }[\"SupabaseSignIn.useEffect\"], [\n        step\n    ]);\n    const handleCodeChange = (index, value)=>{\n        if (value.length <= 1) {\n            const newCode = [\n                ...code\n            ];\n            newCode[index] = value;\n            setCode(newCode);\n            if (value && index < 5) {\n                var _codeInputRefs_current_;\n                (_codeInputRefs_current_ = codeInputRefs.current[index + 1]) === null || _codeInputRefs_current_ === void 0 ? void 0 : _codeInputRefs_current_.focus();\n            }\n            if (index === 5 && value) {\n                const isComplete = newCode.every((digit)=>digit.length === 1);\n                if (isComplete) {\n                    handleOTPVerification(newCode.join(''));\n                }\n            }\n        }\n    };\n    const handleOTPVerification = async (otpCode)=>{\n        const result = await verifyOTP(email, otpCode);\n        if (result.success) {\n            // Após verificar o OTP no signup, o usuário deve definir uma senha\n            if (mode === 'signup') {\n                setStep('password');\n            } else {\n                // Se for signin com OTP, vai direto para sucesso\n                setReverseCanvasVisible(true);\n                setTimeout(()=>{\n                    setInitialCanvasVisible(false);\n                }, 50);\n                setTimeout(()=>{\n                    setStep('success');\n                }, 2000);\n            }\n        }\n    };\n    const handleKeyDown = (index, e)=>{\n        if (e.key === 'Backspace' && !code[index] && index > 0) {\n            var _codeInputRefs_current_;\n            (_codeInputRefs_current_ = codeInputRefs.current[index - 1]) === null || _codeInputRefs_current_ === void 0 ? void 0 : _codeInputRefs_current_.focus();\n        }\n    };\n    const handleBackClick = ()=>{\n        if (step === 'password') {\n            if (mode === 'signup') {\n                // No signup, volta para o código OTP\n                setStep('code');\n            } else {\n                // No signin, volta para o email\n                setStep('email');\n            }\n            setPassword('');\n        } else if (step === 'code') {\n            setStep('email');\n            setCode([\n                '',\n                '',\n                '',\n                '',\n                '',\n                ''\n            ]);\n        }\n        setReverseCanvasVisible(false);\n        setInitialCanvasVisible(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)('flex w-[100%] flex-col min-h-screen bg-black relative', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-0\",\n                children: [\n                    initialCanvasVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sign_in_flow_1__WEBPACK_IMPORTED_MODULE_7__.CanvasRevealEffect, {\n                            animationSpeed: 3,\n                            containerClassName: \"bg-black\",\n                            colors: [\n                                [\n                                    8,\n                                    210,\n                                    133\n                                ],\n                                [\n                                    255,\n                                    255,\n                                    255\n                                ]\n                            ],\n                            dotSize: 6,\n                            reverse: false\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, undefined),\n                    reverseCanvasVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sign_in_flow_1__WEBPACK_IMPORTED_MODULE_7__.CanvasRevealEffect, {\n                            animationSpeed: 4,\n                            containerClassName: \"bg-black\",\n                            colors: [\n                                [\n                                    8,\n                                    210,\n                                    133\n                                ],\n                                [\n                                    255,\n                                    255,\n                                    255\n                                ]\n                            ],\n                            dotSize: 6,\n                            reverse: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(0,0,0,1)_0%,_transparent_100%)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 right-0 h-1/3 bg-gradient-to-b from-black to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex flex-col flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MiniNavbar, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 flex-col lg:flex-row\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col justify-center items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mt-[150px] max-w-sm\",\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm text-center\",\n                                        children: error.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: step === 'email' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -100\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -100\n                                            },\n                                            transition: {\n                                                duration: 0.4,\n                                                ease: 'easeOut'\n                                            },\n                                            className: \"space-y-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white\",\n                                                                children: mode === 'signin' ? 'Bem-vindo de volta' : 'Criar conta'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-[1.8rem] text-white/70 font-light\",\n                                                                children: mode === 'signin' ? 'Entre na sua conta' : 'Junte-se a nós hoje'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleGoogleSignIn,\n                                                            disabled: isLoading,\n                                                            className: \"backdrop-blur-[2px] w-full flex items-center justify-center gap-2 bg-white/5 hover:bg-white/10 text-white border border-white/10 rounded-full py-3 px-4 transition-colors disabled:opacity-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: \"G\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        mode === 'signin' ? 'Entrar' : 'Registrar',\n                                                                        \" com Google\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-px bg-white/10 flex-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white/40 text-sm\",\n                                                                    children: \"ou\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-px bg-white/10 flex-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                            onSubmit: handleEmailSubmit,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"email\",\n                                                                        placeholder: \"<EMAIL>\",\n                                                                        value: email,\n                                                                        onChange: (e)=>setEmail(e.target.value),\n                                                                        disabled: isLoading,\n                                                                        className: \"w-full backdrop-blur-[1px] text-white border-1 border-white/10 rounded-full py-3 px-4 focus:outline-none focus:border focus:border-white/30 text-center disabled:opacity-50\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"submit\",\n                                                                        disabled: isLoading,\n                                                                        className: \"absolute right-1.5 top-1.5 text-white w-9 h-9 flex items-center justify-center rounded-full bg-white/10 hover:bg-white/20 transition-colors group overflow-hidden disabled:opacity-50\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"relative w-full h-full block overflow-hidden\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"absolute inset-0 flex items-center justify-center transition-transform duration-300 group-hover:translate-x-full\",\n                                                                                    children: \"→\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                                    lineNumber: 398,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"absolute inset-0 flex items-center justify-center transition-transform duration-300 -translate-x-full group-hover:translate-x-0\",\n                                                                                    children: \"→\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                                    lineNumber: 401,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setMode(mode === 'signin' ? 'signup' : 'signin'),\n                                                        className: \"text-white/50 hover:text-white/70 transition-colors text-sm\",\n                                                        children: mode === 'signin' ? 'Não tem conta? Registre-se' : 'Já tem conta? Entre'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-white/40 pt-10\",\n                                                    children: [\n                                                        \"Ao continuar, voc\\xea concorda com os\",\n                                                        ' ',\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"#\",\n                                                            className: \"underline text-white/40 hover:text-white/60 transition-colors\",\n                                                            children: \"Termos de Servi\\xe7o\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        ' ',\n                                                        \"e\",\n                                                        ' ',\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"#\",\n                                                            className: \"underline text-white/40 hover:text-white/60 transition-colors\",\n                                                            children: \"Pol\\xedtica de Privacidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \".\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, \"email-step\", true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 19\n                                        }, undefined) : step === 'password' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 100\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: 100\n                                            },\n                                            transition: {\n                                                duration: 0.4,\n                                                ease: 'easeOut'\n                                            },\n                                            className: \"space-y-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white\",\n                                                            children: mode === 'signup' ? 'Crie sua senha' : 'Digite sua senha'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[1.25rem] text-white/50 font-light\",\n                                                            children: [\n                                                                \"Para \",\n                                                                email\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handlePasswordSubmit,\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: showPassword ? 'text' : 'password',\n                                                                    placeholder: mode === 'signup' ? 'Crie uma senha segura' : 'Sua senha',\n                                                                    value: password,\n                                                                    onChange: (e)=>setPassword(e.target.value),\n                                                                    disabled: isLoading,\n                                                                    className: \"w-full backdrop-blur-[1px] text-white border-1 border-white/10 rounded-full py-3 px-4 focus:outline-none focus:border focus:border-white/30 text-center disabled:opacity-50\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                                    className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/70 text-sm\",\n                                                                    children: showPassword ? 'Ocultar' : 'Mostrar'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 460,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex w-full gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                                    type: \"button\",\n                                                                    onClick: handleBackClick,\n                                                                    className: \"rounded-full bg-white text-black font-medium px-8 py-3 hover:bg-white/90 transition-colors w-[30%]\",\n                                                                    whileHover: {\n                                                                        scale: 1.02\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.98\n                                                                    },\n                                                                    transition: {\n                                                                        duration: 0.2\n                                                                    },\n                                                                    children: \"Voltar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                                    type: \"submit\",\n                                                                    disabled: isLoading || !password,\n                                                                    className: \"flex-1 rounded-full font-medium py-3 border transition-all duration-300 \".concat(password && !isLoading ? 'bg-white text-black border-transparent hover:bg-white/90 cursor-pointer' : 'bg-[#111] text-white/50 border-white/10 cursor-not-allowed'),\n                                                                    children: isLoading ? mode === 'signup' ? 'Criando conta...' : 'Entrando...' : mode === 'signup' ? 'Criar conta' : 'Entrar'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, \"password-step\", true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 19\n                                        }, undefined) : step === 'code' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 100\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: 100\n                                            },\n                                            transition: {\n                                                duration: 0.4,\n                                                ease: 'easeOut'\n                                            },\n                                            className: \"space-y-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white\",\n                                                            children: \"C\\xf3digo enviado\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[1.25rem] text-white/50 font-light\",\n                                                            children: \"Verifique seu email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative rounded-full py-4 px-5 border border-white/10 bg-transparent\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: code.map((digit, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    ref: (el)=>{\n                                                                                        codeInputRefs.current[i] = el;\n                                                                                    },\n                                                                                    type: \"text\",\n                                                                                    inputMode: \"numeric\",\n                                                                                    pattern: \"[0-9]*\",\n                                                                                    maxLength: 1,\n                                                                                    value: digit,\n                                                                                    onChange: (e)=>handleCodeChange(i, e.target.value),\n                                                                                    onKeyDown: (e)=>handleKeyDown(i, e),\n                                                                                    disabled: isLoading,\n                                                                                    className: \"w-8 text-center text-xl bg-transparent text-white border-none focus:outline-none focus:ring-0 appearance-none disabled:opacity-50\",\n                                                                                    style: {\n                                                                                        caretColor: 'transparent'\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                                    lineNumber: 520,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                !digit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"absolute top-0 left-0 w-full h-full flex items-center justify-center pointer-events-none\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xl text-white\",\n                                                                                        children: \"0\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                                        lineNumber: 537,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                                    lineNumber: 536,\n                                                                                    columnNumber: 35\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                            lineNumber: 519,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        i < 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white/20 text-xl\",\n                                                                            children: \"|\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                            lineNumber: 541,\n                                                                            columnNumber: 41\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, i, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 29\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex w-full gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                            onClick: handleBackClick,\n                                                            className: \"rounded-full bg-white text-black font-medium px-8 py-3 hover:bg-white/90 transition-colors w-[30%]\",\n                                                            whileHover: {\n                                                                scale: 1.02\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.98\n                                                            },\n                                                            transition: {\n                                                                duration: 0.2\n                                                            },\n                                                            children: \"Voltar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                            disabled: isLoading,\n                                                            className: \"flex-1 rounded-full font-medium py-3 border bg-[#111] text-white/50 border-white/10 cursor-not-allowed\",\n                                                            children: isLoading ? 'Verificando...' : 'Digite o código'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, \"code-step\", true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 50\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.4,\n                                                ease: 'easeOut',\n                                                delay: 0.3\n                                            },\n                                            className: \"space-y-6 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white\",\n                                                            children: \"Sucesso!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 575,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[1.25rem] text-white/50 font-light\",\n                                                            children: \"Redirecionando...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    initial: {\n                                                        scale: 0.8,\n                                                        opacity: 0\n                                                    },\n                                                    animate: {\n                                                        scale: 1,\n                                                        opacity: 1\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5,\n                                                        delay: 0.5\n                                                    },\n                                                    className: \"py-10\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mx-auto w-16 h-16 rounded-full bg-gradient-to-br from-white to-white/70 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-8 w-8 text-black\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, \"success-step\", true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\components\\\\auth\\\\SupabaseSignIn.tsx\",\n        lineNumber: 288,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(SupabaseSignIn, \"qomW49GtlqmND1DbRGGFGuEbdK0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c2 = SupabaseSignIn;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"AnimatedNavLink\");\n$RefreshReg$(_c1, \"MiniNavbar\");\n$RefreshReg$(_c2, \"SupabaseSignIn\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/SupabaseSignIn.tsx\n"));

/***/ })

});