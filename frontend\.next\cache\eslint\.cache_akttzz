[{"C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\login\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\AppSidebar.tsx": "4", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "5", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\auth\\SupabaseSignIn.tsx": "6", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\Button.tsx": "7", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-183.tsx": "8", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-356.tsx": "9", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-581.tsx": "10", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\debug\\SupabaseDebug.tsx": "11", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\info-menu.tsx": "12", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\layout\\ConditionalLayout.tsx": "13", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\LoginDemo.tsx": "14", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\logo.tsx": "15", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\Navbar.tsx": "16", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\notification-menu.tsx": "17", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\PortfolioBalance.tsx": "18", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ThemeToggle.tsx": "19", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\avatar-upload.tsx": "20", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\avatar.tsx": "21", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\button.tsx": "22", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\dropdown-menu.tsx": "23", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\label.tsx": "24", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\navigation-menu.tsx": "25", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\popover.tsx": "26", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\sign-in-flow-1.tsx": "27", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\switch.tsx": "28", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\tooltip.tsx": "29", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\user-menu.tsx": "30", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\contexts\\ProfileContext.tsx": "31", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\contexts\\SidebarContext.tsx": "32", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useAuth.ts": "33", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useLocalStorage.ts": "34", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useProfile.ts": "35", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useTheme.ts": "36", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\config.ts": "37", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\supabase.ts": "38", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\utils.ts": "39", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\types\\index.ts": "40", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\utils\\index.ts": "41", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\(dashboard)\\analytics\\page.tsx": "42", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\(dashboard)\\dashboard\\page.tsx": "43", "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\(dashboard)\\profile\\page.tsx": "44"}, {"size": 1271, "mtime": 1753430644195, "results": "45", "hashOfConfig": "46"}, {"size": 223, "mtime": 1753428522935, "results": "47", "hashOfConfig": "46"}, {"size": 102, "mtime": 1753427531887, "results": "48", "hashOfConfig": "46"}, {"size": 16647, "mtime": 1753438949298, "results": "49", "hashOfConfig": "46"}, {"size": 1089, "mtime": 1753429458940, "results": "50", "hashOfConfig": "46"}, {"size": 24842, "mtime": 1753437863099, "results": "51", "hashOfConfig": "46"}, {"size": 1917, "mtime": 1753418806563, "results": "52", "hashOfConfig": "46"}, {"size": 1492, "mtime": 1753420281523, "results": "53", "hashOfConfig": "46"}, {"size": 594, "mtime": 1753424177462, "results": "54", "hashOfConfig": "46"}, {"size": 4444, "mtime": 1753419151447, "results": "55", "hashOfConfig": "46"}, {"size": 0, "mtime": 1753433329752, "results": "56", "hashOfConfig": "46"}, {"size": 2012, "mtime": 1753429650737, "results": "57", "hashOfConfig": "46"}, {"size": 2616, "mtime": 1753433736749, "results": "58", "hashOfConfig": "46"}, {"size": 242, "mtime": 1753427369704, "results": "59", "hashOfConfig": "46"}, {"size": 293, "mtime": 1753420738208, "results": "60", "hashOfConfig": "46"}, {"size": 1363, "mtime": 1753438821653, "results": "61", "hashOfConfig": "46"}, {"size": 4733, "mtime": 1753429671725, "results": "62", "hashOfConfig": "46"}, {"size": 6097, "mtime": 1753457316734, "results": "63", "hashOfConfig": "46"}, {"size": 1589, "mtime": 1753434084613, "results": "64", "hashOfConfig": "46"}, {"size": 5404, "mtime": 1753457177590, "results": "65", "hashOfConfig": "46"}, {"size": 1109, "mtime": 1753419126418, "results": "66", "hashOfConfig": "46"}, {"size": 1867, "mtime": 1753419126431, "results": "67", "hashOfConfig": "46"}, {"size": 9326, "mtime": 1753419126465, "results": "68", "hashOfConfig": "46"}, {"size": 595, "mtime": 1753420281548, "results": "69", "hashOfConfig": "46"}, {"size": 6606, "mtime": 1753419126498, "results": "70", "hashOfConfig": "46"}, {"size": 1827, "mtime": 1753419126503, "results": "71", "hashOfConfig": "46"}, {"size": 32007, "mtime": 1753457299287, "results": "72", "hashOfConfig": "46"}, {"size": 1036, "mtime": 1753420281543, "results": "73", "hashOfConfig": "46"}, {"size": 1891, "mtime": 1753424177494, "results": "74", "hashOfConfig": "46"}, {"size": 6583, "mtime": 1753438885270, "results": "75", "hashOfConfig": "46"}, {"size": 7716, "mtime": 1753456614510, "results": "76", "hashOfConfig": "46"}, {"size": 1580, "mtime": 1753425131816, "results": "77", "hashOfConfig": "46"}, {"size": 4148, "mtime": 1753456651697, "results": "78", "hashOfConfig": "46"}, {"size": 1081, "mtime": 1753455879431, "results": "79", "hashOfConfig": "46"}, {"size": 214, "mtime": 1753433704921, "results": "80", "hashOfConfig": "46"}, {"size": 1375, "mtime": 1753424805039, "results": "81", "hashOfConfig": "46"}, {"size": 495, "mtime": 1753418795255, "results": "82", "hashOfConfig": "46"}, {"size": 435, "mtime": 1753428387847, "results": "83", "hashOfConfig": "46"}, {"size": 166, "mtime": 1753419075615, "results": "84", "hashOfConfig": "46"}, {"size": 296, "mtime": 1753418781941, "results": "85", "hashOfConfig": "46"}, {"size": 964, "mtime": 1753456634130, "results": "86", "hashOfConfig": "46"}, {"size": 526, "mtime": 1753458340677, "results": "87", "hashOfConfig": "46"}, {"size": 1100, "mtime": 1753458350107, "results": "88", "hashOfConfig": "46"}, {"size": 14656, "mtime": 1753458487964, "results": "89", "hashOfConfig": "46"}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1pczg4f", {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\AppSidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\auth\\SupabaseSignIn.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\Button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-183.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-356.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\comp-581.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\debug\\SupabaseDebug.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\info-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\layout\\ConditionalLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\LoginDemo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\logo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\notification-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\PortfolioBalance.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ThemeToggle.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\avatar-upload.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\sign-in-flow-1.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\components\\user-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\contexts\\ProfileContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\contexts\\SidebarContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useAuth.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useLocalStorage.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useProfile.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\hooks\\useTheme.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\utils\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\(dashboard)\\analytics\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\(dashboard)\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\3\\frontend\\src\\app\\(dashboard)\\profile\\page.tsx", [], []]