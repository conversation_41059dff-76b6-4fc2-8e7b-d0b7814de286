'use client'

import { useState, useEffect } from 'react'
import { supabase, type AuthError } from '@/lib/supabase'
import { User, Session } from '@supabase/supabase-js'

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<AuthError | null>(null)

  useEffect(() => {
    // Obter sessão inicial
    const getInitialSession = async () => {
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        setError({ message: error.message })
      } else {
        setSession(session)
        setUser(session?.user ?? null)
      }
      
      setIsLoading(false)
    }

    getInitialSession()

    // Escutar mudanças de autenticação
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session)
        setUser(session?.user ?? null)
        setIsLoading(false)
        
        if (event === 'SIGNED_OUT') {
          setError(null)
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  // Função para login com email/senha
  const signInWithEmail = async (email: string, password: string) => {
    setIsLoading(true)
    setError(null)

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      setError({ message: error.message })
      setIsLoading(false)
      return { success: false, error }
    }

    setIsLoading(false)
    return { success: true, data }
  }

  // Função para registro com email/senha (direto)
  const signUpWithEmail = async (email: string, password: string) => {
    setIsLoading(true)
    setError(null)

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    })

    if (error) {
      setError({ message: error.message })
      setIsLoading(false)
      return { success: false, error }
    }

    setIsLoading(false)
    return { success: true, data }
  }

  // Função para finalizar signup após OTP (definir senha final)
  const completeSignupWithPassword = async (email: string, password: string) => {
    setIsLoading(true)
    setError(null)

    // Fazer signup com a senha definida pelo usuário
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    })

    if (error) {
      setError({ message: error.message })
      setIsLoading(false)
      return { success: false, error }
    }

    setIsLoading(false)
    return { success: true, data }
  }

  // Função para login com Google
  const signInWithGoogle = async () => {
    setIsLoading(true)
    setError(null)

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/dashboard`
      }
    })

    if (error) {
      setError({ message: error.message })
      setIsLoading(false)
      return { success: false, error }
    }

    return { success: true, data }
  }

  // Função para enviar OTP por email (simulada para signup)
  const signUpWithOTP = async (email: string) => {
    setIsLoading(true)
    setError(null)

    // Simular envio de OTP
    // Em uma implementação real, você enviaria um email com código
    console.log(`Código OTP enviado para ${email}: 123456`)

    setIsLoading(false)
    return { success: true, data: { email } }
  }

  // Função para verificar OTP (simulada para signup)
  const verifySignupOTP = async (email: string, token: string) => {
    setIsLoading(true)
    setError(null)

    // Simular verificação do OTP
    // Aceitar o código 123456 como válido
    if (token === '123456') {
      setIsLoading(false)
      return { success: true, data: { email } }
    } else {
      setError({ message: 'Código inválido. Use: 123456' })
      setIsLoading(false)
      return { success: false, error: { message: 'Código inválido' } }
    }
  }

  // Função para logout
  const signOut = async () => {
    setIsLoading(true)
    const { error } = await supabase.auth.signOut()
    
    if (error) {
      setError({ message: error.message })
    }
    
    setIsLoading(false)
    return { success: !error, error }
  }

  return {
    user,
    session,
    isLoading,
    error,
    signInWithEmail,
    signUpWithEmail,
    completeSignupWithPassword,
    signInWithGoogle,
    signUpWithOTP,
    verifySignupOTP,
    signOut,
  }
}