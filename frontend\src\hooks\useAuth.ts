'use client'

import { useState, useEffect } from 'react'
import { supabase, type AuthError } from '@/lib/supabase'
import { User, Session } from '@supabase/supabase-js'

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<AuthError | null>(null)

  useEffect(() => {
    // Obter sessão inicial
    const getInitialSession = async () => {
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        setError({ message: error.message })
      } else {
        setSession(session)
        setUser(session?.user ?? null)
      }
      
      setIsLoading(false)
    }

    getInitialSession()

    // Escutar mudanças de autenticação
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session)
        setUser(session?.user ?? null)
        setIsLoading(false)
        
        if (event === 'SIGNED_OUT') {
          setError(null)
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  // Função para login com email/senha
  const signInWithEmail = async (email: string, password: string) => {
    setIsLoading(true)
    setError(null)

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      setError({ message: error.message })
      setIsLoading(false)
      return { success: false, error }
    }

    setIsLoading(false)
    return { success: true, data }
  }

  // Função para registro com email/senha (direto)
  const signUpWithEmail = async (email: string, password: string) => {
    setIsLoading(true)
    setError(null)

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    })

    if (error) {
      setError({ message: error.message })
      setIsLoading(false)
      return { success: false, error }
    }

    setIsLoading(false)
    return { success: true, data }
  }

  // Função para finalizar signup após OTP (definir senha final)
  const completeSignupWithPassword = async (email: string, password: string) => {
    setIsLoading(true)
    setError(null)

    // Atualizar a senha do usuário
    const { data, error } = await supabase.auth.updateUser({
      password: password
    })

    if (error) {
      // Se falhar, tentar fazer signup novamente com a senha correta
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
      })

      if (signUpError) {
        setError({ message: signUpError.message })
        setIsLoading(false)
        return { success: false, error: signUpError }
      }

      setIsLoading(false)
      return { success: true, data: signUpData }
    }

    setIsLoading(false)
    return { success: true, data }
  }

  // Função para login com Google
  const signInWithGoogle = async () => {
    setIsLoading(true)
    setError(null)

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/dashboard`
      }
    })

    if (error) {
      setError({ message: error.message })
      setIsLoading(false)
      return { success: false, error }
    }

    return { success: true, data }
  }

  // Função para enviar OTP por email (para signup)
  const signUpWithOTP = async (email: string) => {
    setIsLoading(true)
    setError(null)

    // Primeiro, verificamos se o usuário já existe
    const { data: existingUser } = await supabase.auth.signInWithPassword({
      email,
      password: 'test-password-that-will-fail'
    })

    // Se não der erro de "Invalid login credentials", significa que o usuário existe
    // Vamos usar signUp que enviará um email de confirmação
    const { data, error } = await supabase.auth.signUp({
      email,
      password: 'temp-password-' + Math.random().toString(36), // Senha temporária
    })

    if (error) {
      setError({ message: error.message })
      setIsLoading(false)
      return { success: false, error }
    }

    setIsLoading(false)
    return { success: true, data }
  }

  // Função para verificar OTP (para signup)
  const verifySignupOTP = async (email: string, token: string) => {
    setIsLoading(true)
    setError(null)

    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: 'signup'
    })

    if (error) {
      setError({ message: error.message })
      setIsLoading(false)
      return { success: false, error }
    }

    // Fazer logout imediatamente para não autenticar o usuário ainda
    await supabase.auth.signOut()

    setIsLoading(false)
    return { success: true, data }
  }

  // Função para logout
  const signOut = async () => {
    setIsLoading(true)
    const { error } = await supabase.auth.signOut()
    
    if (error) {
      setError({ message: error.message })
    }
    
    setIsLoading(false)
    return { success: !error, error }
  }

  return {
    user,
    session,
    isLoading,
    error,
    signInWithEmail,
    signUpWithEmail,
    completeSignupWithPassword,
    signInWithGoogle,
    signUpWithOTP,
    verifySignupOTP,
    signOut,
  }
}